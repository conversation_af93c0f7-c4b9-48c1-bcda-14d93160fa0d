package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.dto.UserCourseDTO;
import org.example.entity.UserCourse;

import java.util.List;

/**
 * 用户课程服务接口
 */
public interface UserCourseService {
    
    /**
     * 添加用户课时
     * 
     * @param userCourse 用户课时信息
     *                  - userId: 用户ID
     *                  - totalHours: 总课时
     *                  - completedHours: 已完成课时
     *                  - remainingHours: 剩余课时
     * @return 操作结果
     */
    boolean addUserCourse(UserCourse userCourse);
    
    /**
     * 更新用户课时
     * 
     * @param userCourse 用户课时信息
     *                  - id: 课时记录ID（必填）
     *                  - totalHours: 总课时（可选）
     *                  - completedHours: 已完成课时（可选）
     *                  - remainingHours: 剩余课时（可选）
     * @return 操作结果
     */
    boolean updateUserCourse(UserCourse userCourse);
    
    /**
     * 获取用户课程详情
     * 
     * @param id 课时记录ID
     * @return 用户课时详情
     */
    UserCourse getUserCourse(Long id);
    
    /**
     * 获取用户的所有课程
     * 
     * @param userId 用户ID
     * @return 用户课时列表
     */
    List<UserCourse> getUserCourses(Long userId);
    
    /**
     * 增加已完成课时
     * 
     * @param id 课时记录ID
     * @param hours 增加的课时数
     * @return 操作结果
     */
    boolean addCompletedHours(Long id, Double hours);
    
    /**
     * 增加剩余课时(购买课时)
     * 
     * @param id 课时记录ID
     * @param hours 增加的课时数（必须大于0）
     * @return 操作结果
     */
    boolean addRemainingHours(Long id, Double hours);
    
    /**
     * 分页查询学员课时信息
     * 
     * @param current 当前页码
     * @param size 每页记录数
     * @param username 学员姓名（可选，模糊查询）
     * @param studentType 学员类型（可选）
     * @return 学员课时信息列表及分页信息
     */
    Page<UserCourseDTO> pageUserCourseInfo(int current, int size, String username, String studentType);
    
    /**
     * 获取学员课时详情
     * 
     * @param id 课时记录ID
     * @return 学员课时详情（包含用户信息）
     */
    UserCourseDTO getUserCourseInfo(Long id);
    
    /**
     * 初始化学员课时
     * 
     * @param userId 学员ID
     * @param studentType 学员类型
     * @return 操作结果
     * 
     * 业务规则：
     * - 根据学员类型设置默认课时值：
     *   - 运动类执照学员: 30.0小时
     *   - 私用驾驶员执照学员: 40.0小时
     *   - 运动类教员学员: 150.0小时
     */
    boolean initUserCourse(Long userId, String studentType);
	
	/**
	 * 根据用户ID查询用户课时信息
	 */
	UserCourse getUserCourseByUserId(Long userId);
}
