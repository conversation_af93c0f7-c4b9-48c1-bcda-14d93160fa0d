package org.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.Formatter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;

/**
 * 日期格式化配置
 * 支持多种日期格式的自动转换
 */
@Configuration
public class DateFormatConfig {
	
	@Bean
	public Formatter<LocalDate> localDateFormatter() {
		return new Formatter<LocalDate>() {
			@Override
			public LocalDate parse(String text, Locale locale) {
				// 支持多种格式，包括"yyyy-M-d"、"yyyy/M/d"等
				DateTimeFormatter[] formatters = {
						DateTimeFormatter.ISO_LOCAL_DATE,  // 标准格式 yyyy-MM-dd
						DateTimeFormatter.ofPattern("yyyy-M-d"),  // 简化格式 yyyy-M-d
						DateTimeFormatter.ofPattern("yyyy/M/d"),  // 斜杠分隔 yyyy/M/d
						DateTimeFormatter.ofPattern("yyyy/MM/dd") // 斜杠分隔 yyyy/MM/dd
				};
				
				for (DateTimeFormatter formatter : formatters) {
					try {
						return LocalDate.parse(text, formatter);
					} catch (DateTimeParseException e) {
						// 尝试下一个格式
					}
				}
				throw new IllegalArgumentException("无法解析日期: " + text);
			}
			
			@Override
			public String print(LocalDate object, Locale locale) {
				return object.format(DateTimeFormatter.ISO_LOCAL_DATE);
			}
		};
	}
}