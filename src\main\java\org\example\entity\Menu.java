package org.example.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单实体类
 */
@Data
@TableName("sys_menu")
public class Menu implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 父菜单ID，一级菜单为0
     */
    @JsonProperty(value = "parent_id", access = JsonProperty.Access.WRITE_ONLY)
    @TableField("parent_id")
    private Long parentId;
    
    /**
     * 为了兼容前端驼峰命名的字段，添加getter方法
     */
    @JsonProperty("parentId")
    public Long getParentIdCamel() {
        return parentId;
    }
    
    /**
     * 菜单名称
     */
    private String name;
    
    /**
     * 菜单URL
     */
    private String url;
    
    /**
     * 类型：0 目录，1 菜单，2 按钮
     */
    private Integer type;
    
    /**
     * 菜单图标
     */
    private String icon;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 是否为iframe，0否，1是
     */
    private Integer iframe;
    
    /**
     * 按钮权限标识，如add、edit等
     */
    private String menu;
    
    /**
     * 状态：0禁用，1正常
     */
    private Integer status;
    
    /**
     * 子菜单列表
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Menu> children;
} 