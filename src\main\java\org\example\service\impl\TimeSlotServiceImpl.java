package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.example.constants.Constants;
import org.example.dto.BatchTimeSlotDTO;
import org.example.dto.TimeSlotDTO;
import org.example.dto.TimeSlotTemplateDTO;
import org.example.entity.TimeSlot;
import org.example.mapper.TimeSlotMapper;
import org.example.service.TimeSlotService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.example.entity.Appointment;
import org.example.mapper.AppointmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 时间段服务实现类
 */
@Service
@Slf4j
public class TimeSlotServiceImpl extends ServiceImpl<TimeSlotMapper, TimeSlot> implements TimeSlotService {
    
    @Autowired
    private AppointmentMapper appointmentMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    @Transactional
    public boolean addTimeSlot(TimeSlotDTO timeSlotDTO) {
        // 检查时间段是否有冲突
        if (checkTimeConflict(timeSlotDTO.getStartTime(), timeSlotDTO.getEndTime(), null)) {
            return false;
        }
        
        TimeSlot timeSlot = new TimeSlot();
        BeanUtils.copyProperties(timeSlotDTO, timeSlot);
        
        // 设置默认值
        if (timeSlot.getStatus() == null) {
            timeSlot.setStatus(Constants.TIME_SLOT_STATUS_AVAILABLE);
        }
        if (timeSlot.getExperienceCapacity() == null) {
            timeSlot.setExperienceCapacity(1); // 默认容量为1
        }
        if (timeSlot.getTrainingCapacity() == null) {
            timeSlot.setTrainingCapacity(1); // 默认容量为1
        }
        if (timeSlot.getExperienceBookedCount() == null) {
            timeSlot.setExperienceBookedCount(0);
        }
        if (timeSlot.getTrainingBookedCount() == null) {
            timeSlot.setTrainingBookedCount(0);
        }
        
        return save(timeSlot);
    }
    
    @Override
    @Transactional
    public boolean updateTimeSlot(TimeSlotDTO timeSlotDTO) {
        // 检查时间段是否有冲突
        if (checkTimeConflict(timeSlotDTO.getStartTime(), timeSlotDTO.getEndTime(), timeSlotDTO.getId())) {
            return false;
        }
        
        TimeSlot timeSlot = getById(timeSlotDTO.getId());
        if (timeSlot == null) {
            return false;
        }
        
        BeanUtils.copyProperties(timeSlotDTO, timeSlot);
        return updateById(timeSlot);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTimeSlot(Long id) {
        log.info("开始删除时间段ID: {}", id);
        
        try {
            // 使用直接SQL更新语句，将关联预约的time_slot_id设置为null
            String updateSql = "UPDATE appointment SET time_slot_id = NULL, status = 'COMPLETED' WHERE time_slot_id = ?";
            int updatedCount = jdbcTemplate.update(updateSql, id);
            
            log.info("已通过SQL直接更新时间段ID {} 的 {} 个关联预约", id, updatedCount);
            
            // 确保所有关联预约都已更新
            LambdaQueryWrapper<Appointment> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(Appointment::getTimeSlotId, id);
            long remainingCount = appointmentMapper.selectCount(checkWrapper);
            
            if (remainingCount > 0) {
                log.error("仍有 {} 个预约记录关联到时间段ID {}", remainingCount, id);
                return false;
            }
            
            // 删除时间段
            boolean deleted = removeById(id);
            
            if (!deleted) {
                log.error("删除时间段ID {} 失败", id);
                return false;
            }
            
            log.info("成功删除时间段ID: {}", id);
            return true;
        } catch (Exception e) {
            log.error("删除时间段ID {} 时发生错误: {}", id, e.getMessage());
            throw e;
        }
    }
    
    @Override
    public TimeSlot getTimeSlot(Long id) {
        return getById(id);
    }
    
    @Override
    public TimeSlotDTO getTimeSlotDTO(Long id) {
        TimeSlot timeSlot = getById(id);
        if (timeSlot == null) {
            return null;
        }
        
        TimeSlotDTO dto = new TimeSlotDTO();
        BeanUtils.copyProperties(timeSlot, dto);
        return dto;
    }
    
    @Override
    public List<TimeSlot> listAvailableTimeSlots() {
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TimeSlot::getStatus, Constants.TIME_SLOT_STATUS_AVAILABLE);
        wrapper.orderByAsc(TimeSlot::getDate, TimeSlot::getStartTime);
        return list(wrapper);
    }
    
    @Override
    public Page<TimeSlotDTO> pageTimeSlots(int current, int size, Integer status) {
        Page<TimeSlot> page = new Page<>(current, size);
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        if (status != null) {
            wrapper.eq(TimeSlot::getStatus, status);
        }
        wrapper.orderByAsc(TimeSlot::getDate, TimeSlot::getStartTime);
        
        Page<TimeSlot> resultPage = page(page, wrapper);
        
        // 转换为DTO
        Page<TimeSlotDTO> dtoPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<TimeSlotDTO> dtoList = resultPage.getRecords().stream().map(timeSlot -> {
            TimeSlotDTO dto = new TimeSlotDTO();
            BeanUtils.copyProperties(timeSlot, dto);
            return dto;
        }).collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
    
    @Override
    public boolean checkTimeConflict(LocalTime startTime, LocalTime endTime, Long id) {
        if (startTime == null || endTime == null || startTime.isAfter(endTime) || startTime.equals(endTime)) {
            return true; // 无效的时间范围
        }
        
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        // 查找时间范围有重叠的时间段
        // 重叠条件：新时间段的开始时间 < 已有时间段的结束时间 && 新时间段的结束时间 > 已有时间段的开始时间
        wrapper.lt(TimeSlot::getEndTime, endTime)
               .gt(TimeSlot::getStartTime, startTime);
        
        // 如果是更新操作，排除自身
        if (id != null) {
            wrapper.ne(TimeSlot::getId, id);
        }
        
        return count(wrapper) > 0;
    }
    
    @Override
    @Transactional
    public int batchCreateTimeSlots(BatchTimeSlotDTO batchDTO) {
        if (batchDTO.getStartDate() == null || batchDTO.getEndDate() == null || 
            batchDTO.getStartDate().isAfter(batchDTO.getEndDate()) || 
            batchDTO.getTemplates() == null || batchDTO.getTemplates().isEmpty()) {
            return 0;
        }
        
        List<TimeSlot> timeSlots = new ArrayList<>();
        LocalDate currentDate = batchDTO.getStartDate();
        
        while (!currentDate.isAfter(batchDTO.getEndDate())) {
            // 检查日期是否符合重复规则
            if (isDateMatchRule(currentDate, batchDTO.getRepeatRule(), batchDTO.getCustomDays())) {
                for (TimeSlotTemplateDTO template : batchDTO.getTemplates()) {
                    TimeSlot timeSlot = new TimeSlot();
                    timeSlot.setDate(currentDate);
                    timeSlot.setStartTime(template.getStartTime());
                    timeSlot.setEndTime(template.getEndTime());
                    timeSlot.setPurpose(template.getPurpose());
                    timeSlot.setDescription(template.getDescription());
                    timeSlot.setStatus(Constants.TIME_SLOT_STATUS_AVAILABLE);
                    timeSlot.setExperienceCapacity(template.getExperienceCapacity() != null ? template.getExperienceCapacity() : 1);
                    timeSlot.setTrainingCapacity(template.getTrainingCapacity() != null ? template.getTrainingCapacity() : 1);
                    timeSlot.setExperienceBookedCount(0);
                    timeSlot.setTrainingBookedCount(0);
                    
                    timeSlots.add(timeSlot);
                }
            }
            currentDate = currentDate.plusDays(1);
        }
        
        if (!timeSlots.isEmpty()) {
            saveBatch(timeSlots);
        }
        
        return timeSlots.size();
    }
    
    /**
     * 检查日期是否符合重复规则
     */
    private boolean isDateMatchRule(LocalDate date, String repeatRule, List<Integer> customDays) {
        if (repeatRule == null) {
            return true; // 默认每天
        }
        
        switch (repeatRule) {
            case "ALL":
                return true; // 每天
            case "WEEKDAY":
                // 工作日(周一至周五)
                DayOfWeek dayOfWeek = date.getDayOfWeek();
                return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
            case "WEEKEND":
                // 周末(周六和周日)
                DayOfWeek dayOfWeekend = date.getDayOfWeek();
                return dayOfWeekend == DayOfWeek.SATURDAY || dayOfWeekend == DayOfWeek.SUNDAY;
            case "CUSTOM":
                // 自定义(指定星期几)
                if (customDays == null || customDays.isEmpty()) {
                    return false;
                }
                // 将Java的DayOfWeek(1-7表示周一到周日)转换为customDays中的表示
                int dayValue = date.getDayOfWeek().getValue();
                return customDays.contains(dayValue);
            default:
                return true;
        }
    }
    
    @Override
    public List<TimeSlotDTO> listTimeSlotsByDate(LocalDate date, String purpose) {
        if (date == null) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TimeSlot::getDate, date);
        wrapper.eq(TimeSlot::getStatus, Constants.TIME_SLOT_STATUS_AVAILABLE);
        
        // 如果指定了用途，则过滤
        if (StringUtils.hasText(purpose)) {
            wrapper.and(w -> w.eq(TimeSlot::getPurpose, purpose)
                    .or()
                    .eq(TimeSlot::getPurpose, Constants.TIME_SLOT_PURPOSE_ALL));
        }
        
        wrapper.orderByAsc(TimeSlot::getStartTime);
        
        List<TimeSlot> timeSlots = list(wrapper);
        return timeSlots.stream().map(timeSlot -> {
            TimeSlotDTO dto = new TimeSlotDTO();
            BeanUtils.copyProperties(timeSlot, dto);
            return dto;
        }).collect(Collectors.toList());
    }
    
    @Override
    public List<TimeSlotDTO> listTimeSlotsByDateRange(LocalDate startDate, LocalDate endDate, String purpose) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(TimeSlot::getDate, startDate);
        wrapper.le(TimeSlot::getDate, endDate);
        wrapper.eq(TimeSlot::getStatus, Constants.TIME_SLOT_STATUS_AVAILABLE);
        
        // 如果指定了用途，则过滤
        if (StringUtils.hasText(purpose)) {
            wrapper.and(w -> w.eq(TimeSlot::getPurpose, purpose)
                    .or()
                    .eq(TimeSlot::getPurpose, Constants.TIME_SLOT_PURPOSE_ALL));
        }
        
        wrapper.orderByAsc(TimeSlot::getDate, TimeSlot::getStartTime);
        
        List<TimeSlot> timeSlots = list(wrapper);
        return timeSlots.stream().map(timeSlot -> {
            TimeSlotDTO dto = new TimeSlotDTO();
            BeanUtils.copyProperties(timeSlot, dto);
            return dto;
        }).collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public boolean updateBookedStatus(Long id, boolean isExperience, boolean booked) {
        // 这个方法已经不需要了，因为我们现在只使用BookedCount来判断
        // 通过调用incrementBookedCount或decrementBookedCount来更新状态
        return true;
    }
    
    @Override
    @Transactional
    public boolean incrementBookedCount(Long id, boolean isExperience) {
        TimeSlot timeSlot = getById(id);
        if (timeSlot == null) {
            return false;
        }
        
        // 检查容量是否已满
        if (isExperience) {
            if (timeSlot.getExperienceBookedCount() >= timeSlot.getExperienceCapacity()) {
                return false;
            }
            timeSlot.setExperienceBookedCount(timeSlot.getExperienceBookedCount() + 1);
        } else {
            if (timeSlot.getTrainingBookedCount() >= timeSlot.getTrainingCapacity()) {
                return false;
            }
            timeSlot.setTrainingBookedCount(timeSlot.getTrainingBookedCount() + 1);
        }
        
        return updateById(timeSlot);
    }
    
    @Override
    @Transactional
    public boolean decrementBookedCount(Long id, boolean isExperience) {
        TimeSlot timeSlot = getById(id);
        if (timeSlot == null) {
            return false;
        }
        
        if (isExperience) {
            if (timeSlot.getExperienceBookedCount() > 0) {
                timeSlot.setExperienceBookedCount(timeSlot.getExperienceBookedCount() - 1);
            }
        } else {
            if (timeSlot.getTrainingBookedCount() > 0) {
                timeSlot.setTrainingBookedCount(timeSlot.getTrainingBookedCount() - 1);
            }
        }
        
        return updateById(timeSlot);
    }
    
    @Override
    public boolean hasAvailableCapacity(Long id, boolean isExperience) {
        TimeSlot timeSlot = getById(id);
        if (timeSlot == null) {
            return false;
        }
        
        if (isExperience) {
            return timeSlot.getExperienceBookedCount() < timeSlot.getExperienceCapacity();
        } else {
            return timeSlot.getTrainingBookedCount() < timeSlot.getTrainingCapacity();
        }
    }
    
    @Override
    @Transactional
    public int batchUpdateStatus(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        LambdaUpdateWrapper<TimeSlot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(TimeSlot::getId, ids)
                     .set(TimeSlot::getStatus, status);
        
        update(updateWrapper);
        return ids.size();
    }
    
    @Override
    public boolean setDateSlotsStatus(LocalDate date, Integer status, String description) {
        LambdaUpdateWrapper<TimeSlot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TimeSlot::getDate, date)
                     .set(TimeSlot::getStatus, status)
                     .set(TimeSlot::getDescription, description);
        
        return update(updateWrapper);
    }
    
    @Override
    public boolean setDateRangeSlotsStatus(LocalDate startDate, LocalDate endDate, Integer status, String description) {
        LambdaUpdateWrapper<TimeSlot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.between(TimeSlot::getDate, startDate, endDate)
                     .set(TimeSlot::getStatus, status)
                     .set(TimeSlot::getDescription, description);
        
        return update(updateWrapper);
    }
    
    @Override
    @Transactional
    public boolean createAndSetDateSlots(LocalDate date, Integer status, String description) {
        // 检查该日期是否已有时间段
        LambdaQueryWrapper<TimeSlot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TimeSlot::getDate, date);
        long count = count(queryWrapper);
        
        if (count == 0) {
            // 如果没有时间段，创建默认的时间段
            List<TimeSlot> defaultSlots = createDefaultTimeSlotsForDate(date, status, description);
            return saveBatch(defaultSlots);
        } else {
            // 如果已有时间段，只更新状态
            return setDateSlotsStatus(date, status, description);
        }
    }
    
    /**
     * 为指定日期创建默认时间段列表
     * @param date 日期
     * @param status 状态
     * @param description 描述
     * @return 时间段列表
     */
    private List<TimeSlot> createDefaultTimeSlotsForDate(LocalDate date, Integer status, String description) {
        List<TimeSlot> slots = new ArrayList<>();
        
        // 默认时间段设置，例如上午9点到下午5点，每小时一个时间段
        LocalTime[] startTimes = {
            LocalTime.of(9, 0),
            LocalTime.of(10, 0),
            LocalTime.of(11, 0),
            LocalTime.of(13, 0),
            LocalTime.of(14, 0),
            LocalTime.of(15, 0),
            LocalTime.of(16, 0)
        };
        
        for (LocalTime startTime : startTimes) {
            TimeSlot slot = new TimeSlot();
            slot.setDate(date);
            slot.setStartTime(startTime);
            slot.setEndTime(startTime.plusHours(1));
            slot.setStatus(status);
            slot.setDescription(description);
            slot.setPurpose(Constants.TIME_SLOT_PURPOSE_ALL); // 默认用途为ALL，表示体验和培训都可以
            
            // 设置默认容量
            slot.setExperienceCapacity(3); // 体验默认容量
            slot.setTrainingCapacity(2);   // 培训默认容量
            
            // 初始化已预约数量为0
            slot.setExperienceBookedCount(0);
            slot.setTrainingBookedCount(0);
            
            slots.add(slot);
        }
        
        return slots;
    }
    
    @Override
    public List<LocalDate> getAvailableDays(LocalDate startDate, LocalDate endDate, String purpose) {
        log.info("获取可用日期 - 开始日期: {}, 结束日期: {}, 用途: {}", startDate, endDate, purpose);
        
        // 构建查询条件
        LambdaQueryWrapper<TimeSlot> query = new LambdaQueryWrapper<>();
        query.ge(TimeSlot::getDate, startDate)
             .le(TimeSlot::getDate, endDate)
             .eq(TimeSlot::getStatus, Constants.TIME_SLOT_STATUS_AVAILABLE);
        
        // 根据预约用途增加条件
        if ("experience".equalsIgnoreCase(purpose)) {
            query.gt(TimeSlot::getExperienceCapacity, 0)
                 .apply("experience_capacity > experience_booked_count");
        } else if ("training".equalsIgnoreCase(purpose)) {
            query.gt(TimeSlot::getTrainingCapacity, 0)
                 .apply("training_capacity > training_booked_count");
        } else {
            // 默认查询任意用途
            query.and(q -> 
                q.and(subQ -> subQ.gt(TimeSlot::getExperienceCapacity, 0)
                                 .apply("experience_capacity > experience_booked_count"))
                 .or(subQ -> subQ.gt(TimeSlot::getTrainingCapacity, 0)
                                .apply("training_capacity > training_booked_count"))
            );
        }
        
        // 查询符合条件的时间段
        List<TimeSlot> availableSlots = list(query);
        
        // 提取可用日期并去重
        return availableSlots.stream()
                .map(TimeSlot::getDate)
                .distinct()
                .collect(Collectors.toList());
    }
} 