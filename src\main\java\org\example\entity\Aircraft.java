package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞机实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("aircraft")
public class Aircraft extends BaseEntity {
    /**
     * 飞机注册号
     */
    private String registrationNumber;
    
    /**
     * 机型
     */
    private String model;
    
    /**
     * 飞机类型
     */
    private String type;
    
    /**
     * 座位数量
     */
    private Integer seats;
    
    /**
     * 飞机状态: ACTIVE(可用), MAINTENANCE(维护), INACTIVE(不可用)
     */
    private String status;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 描述
     */
    private String description;
} 