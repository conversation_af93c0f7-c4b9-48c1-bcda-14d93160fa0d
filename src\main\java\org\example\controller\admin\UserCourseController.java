package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.constants.Constants;
import org.example.dto.UserCourseDTO;
import org.example.entity.UserCourse;
import org.example.service.UserCourseService;
import org.example.service.UserService;
import org.example.entity.User;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户课时控制器
 */
@RestController
@RequestMapping("/user-courses")
public class UserCourseController {
	
	@Autowired
	private UserCourseService userCourseService;
	
	@Autowired
	private UserService userService;
	
	/**
	 * 分页查询学员课时信息
	 * 
	 * @param current 当前页码（默认1）
	 * @param size 每页记录数（默认10）
	 * @param username 学员姓名（可选，模糊查询）
	 * @param studentType 学员类型（可选）
	 * @return 学员课时信息列表及分页信息
	 */
	@GetMapping("/page")
	public Result<Page<UserCourseDTO>> pageUserCourseInfo(
			@RequestParam(defaultValue = "1") int current,
			@RequestParam(defaultValue = "10") int size,
			@RequestParam(required = false) String username,
			@RequestParam(required = false) String studentType) {
		Page<UserCourseDTO> page = userCourseService.pageUserCourseInfo(current, size, username, studentType);
		return Result.success(page);
	}
	
	/**
	 * 初始化学员课时（通过学员姓名）
	 * 
	 * @param username 学员姓名
	 * @param studentType 学员类型（支持中文描述或编码值）
	 * @return 操作结果
	 * 
	 * 业务规则：
	 * - 根据学员类型设置默认课时值
	 * - 支持中文学员类型描述自动转换为编码值
	 */
	@PostMapping("/init-by-name")
	public Result<Boolean> initUserCourseByName(@RequestParam String username, @RequestParam String studentType) {
		// 根据姓名查询用户
		User user = userService.getUserByUsername(username);
		if (user == null) {
			return Result.error("未找到该学员信息");
		}
		
		// 将中文描述转换为编码值（如果传入的是中文描述）
		String typeCode = convertToStudentTypeCode(studentType);
		if (typeCode == null) {
			return Result.error("无效的学员类型：" + studentType);
		}
		
		// 初始化课时信息，所有课时初始为0
		boolean success = userCourseService.initUserCourse(user.getId(), typeCode);
		return success ? Result.success(true) : Result.error("初始化学员课时失败");
	}
	
	/**
	 * 创建自定义学员课时记录
	 * 
	 * @param username 学员姓名
	 * @param studentType 学员类型（可选）
	 * @param totalHours 总课时（可选，默认0）
	 * @param completedHours 已完成课时（可选，默认0）
	 * @param remainingHours 剩余课时（可选，默认0）
	 * @return 操作结果
	 * 
	 * 业务规则：
	 * - 创建自定义课时记录，不使用默认值
	 * - 如果提供了学员类型，会同时更新用户的学员类型
	 */
	@PostMapping("/create-custom")
	public Result<Boolean> createCustomUserCourse(
			@RequestParam String username,
			@RequestParam(required = false) String studentType,
			@RequestParam(required = false) Double totalHours,
			@RequestParam(required = false) Double completedHours,
			@RequestParam(required = false) Double remainingHours) {
		
		// 根据姓名查询用户
		User user = userService.getUserByUsername(username);
		if (user == null) {
			return Result.error("未找到该学员信息");
		}
		
		// 如果提供了学员类型，则更新用户的学员类型
		if (studentType != null && !studentType.isEmpty()) {
			// 将中文描述转换为编码值（如果传入的是中文描述）
			String typeCode = convertToStudentTypeCode(studentType);
			if (typeCode == null) {
				return Result.error("无效的学员类型：" + studentType);
			}
			
			User updateUser = new User();
			updateUser.setId(user.getId());
			updateUser.setStudentType(typeCode);
			userService.updateUserInfo(updateUser);
		}
		
		// 创建课时记录，不根据学员类型设置默认值，全部使用传入值或设为0.0
		UserCourse userCourse = new UserCourse();
		userCourse.setUserId(user.getId());
		userCourse.setTotalHours(totalHours != null ? totalHours : 0.0);
		userCourse.setCompletedHours(completedHours != null ? completedHours : 0.0);
		userCourse.setRemainingHours(remainingHours != null ? remainingHours : 0.0);
		
		boolean success = userCourseService.addUserCourse(userCourse);
		return success ? Result.success(true) : Result.error("创建学员课时记录失败");
	}
	
	/**
	 * 更新学员课时信息
	 * 
	 * @param id 课时记录ID（路径参数）
	 * @param studentType 学员类型（可选）
	 * @param totalHours 总课时（可选）
	 * @param completedHours 已完成课时（可选）
	 * @param remainingHours 剩余课时（可选）
	 * @return 操作结果
	 * 
	 * 业务规则：
	 * - 验证课时数据的逻辑关系（总课时 = 已完成课时 + 剩余课时，允许0.01误差）
	 * - 如果提供了学员类型，会同时更新用户的学员类型
	 */
	@PutMapping("/{id}")
	public Result<Boolean> updateUserCourse(
			@PathVariable Long id,
			@RequestParam(required = false) String studentType,
			@RequestParam(required = false) Double totalHours,
			@RequestParam(required = false) Double completedHours,
			@RequestParam(required = false) Double remainingHours) {
		
		// 获取当前课时记录
		UserCourse userCourse = userCourseService.getUserCourse(id);
		if (userCourse == null) {
			return Result.error("课时记录不存在");
		}
		
		// 如果提供了学员类型，则更新用户的学员类型
		if (studentType != null && !studentType.isEmpty()) {
			// 将中文描述转换为编码值（如果传入的是中文描述）
			String typeCode = convertToStudentTypeCode(studentType);
			if (typeCode == null) {
				return Result.error("无效的学员类型：" + studentType);
			}
			
			// 获取用户信息
			User user = userService.getUserById(userCourse.getUserId());
			if (user != null) {
				// 更新用户的学员类型
				User updateUser = new User();
				updateUser.setId(user.getId());
				updateUser.setStudentType(typeCode);
				userService.updateUserInfo(updateUser);
			}
		}
		
		// 准备要更新的数据
		Double newTotalHours = totalHours != null ? totalHours : userCourse.getTotalHours();
		Double newCompletedHours = completedHours != null ? completedHours : userCourse.getCompletedHours();
		Double newRemainingHours = remainingHours != null ? remainingHours : userCourse.getRemainingHours();
		
		// 验证课时数据的逻辑关系
		if (newTotalHours < 0) {
			return Result.error("总课时不能为负数");
		}
		
		if (newCompletedHours < 0) {
			return Result.error("已完成课时不能为负数");
		}
		
		if (newRemainingHours < 0) {
			return Result.error("剩余课时不能为负数");
		}
		
		// 验证总课时是否等于已完成课时加剩余课时（允许0.01的误差）
		double diff = Math.abs(newTotalHours - (newCompletedHours + newRemainingHours));
		if (diff > 0.01) {
			return Result.error("课时数据不一致：总课时(" + String.format("%.2f", newTotalHours) + 
				")应等于已完成课时(" + String.format("%.2f", newCompletedHours) + 
				")加剩余课时(" + String.format("%.2f", newRemainingHours) + ")");
		}
		
		// 更新课时信息
		UserCourse updateCourse = new UserCourse();
		updateCourse.setId(id);
		updateCourse.setTotalHours(newTotalHours);
		updateCourse.setCompletedHours(newCompletedHours);
		updateCourse.setRemainingHours(newRemainingHours);
		
		boolean success = userCourseService.updateUserCourse(updateCourse);
		return success ? Result.success(true) : Result.error("更新课时信息失败");
	}
	
	/**
	 * 增加剩余课时(购买课时)
	 * 
	 * @param id 课时记录ID（路径参数）
	 * @param hours 增加的课时数（必须大于0）
	 * @return 操作结果
	 */
	@PostMapping("/{id}/add-hours")
	public Result<Boolean> addRemainingHours(@PathVariable Long id, @RequestParam Double hours) {
		if (hours <= 0) {
			return Result.error("增加的课时必须大于0");
		}
		boolean success = userCourseService.addRemainingHours(id, hours);
		return success ? Result.success(true) : Result.error("增加课时失败");
	}
	
	/**
	 * 获取学员类型列表
	 * 用于前端展示学员类型选择
	 * 
	 * @return 学员类型列表（包含编码值和中文描述）
	 */
	@GetMapping("/student-types")
	public Result<Object> getStudentTypes() {
		return Result.success(new Object[] {
				new Object() {
					public final String value = Constants.STUDENT_TYPE_SPORT_LICENSE;
					public final String label = "运动类执照学员";
				},
				new Object() {
					public final String value = Constants.STUDENT_TYPE_PRIVATE_LICENSE;
					public final String label = "私用驾驶员执照学员";
				},
				new Object() {
					public final String value = Constants.STUDENT_TYPE_SPORT_INSTRUCTOR;
					public final String label = "运动类教员学员";
				}
		});
	}
	
	/**
	 * 将学员类型描述转换为编码值
	 * 支持中文描述或编码值作为输入
	 */
	private String convertToStudentTypeCode(String studentType) {
		// 如果输入的已经是有效的编码值，直接返回
		if (Constants.STUDENT_TYPE_SPORT_LICENSE.equals(studentType) ||
			Constants.STUDENT_TYPE_PRIVATE_LICENSE.equals(studentType) ||
			Constants.STUDENT_TYPE_SPORT_INSTRUCTOR.equals(studentType)) {
			return studentType;
		}
		
		// 如果输入的是中文描述，转换为编码值
		switch (studentType) {
			case "运动类执照学员":
				return Constants.STUDENT_TYPE_SPORT_LICENSE;
			case "私用驾驶员执照学员":
				return Constants.STUDENT_TYPE_PRIVATE_LICENSE;
			case "运动类教员学员":
				return Constants.STUDENT_TYPE_SPORT_INSTRUCTOR;
			default:
				return null; // 无效的学员类型
		}
	}
}