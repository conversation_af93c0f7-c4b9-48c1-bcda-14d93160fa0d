package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.example.dto.FlightExperienceDTO;
import org.example.entity.FlightExperience;
import org.example.service.FlightExperienceService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员飞行体验控制器
 */
@RestController
@RequestMapping("/admin/experience")
@Slf4j
public class AdminFlightExperienceController {

    @Autowired
    private FlightExperienceService flightExperienceService;

    /**
     * 分页查询飞行体验项目（管理员视图，可查看所有状态）
     *
     * @param page   页码
     * @param size   每页大小
     * @param name   项目名称，可选
     * @param status 状态，可选
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Page<FlightExperienceDTO>> page(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status) {

        log.info("管理员分页查询飞行体验项目：page={}, size={}, name={}, status={}", page, size, name, status);
        Page<FlightExperience> pageInfo = new Page<>(page, size);
        Page<FlightExperienceDTO> result = flightExperienceService.page(pageInfo, name, status);
        return Result.success(result);
    }

    /**
     * 获取飞行体验项目详情
     *
     * @param id 项目ID
     * @return 项目详情
     */
    @GetMapping("/{id}")
    public Result<FlightExperienceDTO> getDetail(@PathVariable Long id) {
        log.info("管理员获取飞行体验项目详情：id={}", id);
        FlightExperienceDTO detail = flightExperienceService.getDetail(id);
        return Result.success(detail);
    }

    /**
     * 添加飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 添加结果
     */
    @PostMapping
    public Result<FlightExperienceDTO> add(@RequestBody FlightExperienceDTO flightExperienceDTO) {
        log.info("管理员添加飞行体验项目：{}", flightExperienceDTO);
        FlightExperienceDTO result = flightExperienceService.addExperience(flightExperienceDTO);
        return Result.success(result, "添加飞行体验项目成功");
    }

    /**
     * 修改飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 修改结果
     */
    @PutMapping
    public Result<FlightExperienceDTO> update(@RequestBody FlightExperienceDTO flightExperienceDTO) {
        if (flightExperienceDTO.getId() == null) {
            return Result.error("飞行体验项目ID不能为空");
        }
        log.info("管理员修改飞行体验项目：{}", flightExperienceDTO);
        FlightExperienceDTO result = flightExperienceService.updateExperience(flightExperienceDTO);
        return Result.success(result, "修改飞行体验项目成功");
    }

    /**
     * 删除飞行体验项目
     *
     * @param id 项目ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        log.info("管理员删除飞行体验项目：id={}", id);
        boolean success = flightExperienceService.deleteExperience(id);
        return success ? Result.success(true, "删除飞行体验项目成功") : Result.error("删除飞行体验项目失败");
    }

    /**
     * 上架飞行体验项目
     *
     * @param id 项目ID
     * @return 上架结果
     */
    @PutMapping("/publish/{id}")
    public Result<Boolean> publish(@PathVariable Long id) {
        log.info("管理员上架飞行体验项目：id={}", id);
        boolean success = flightExperienceService.changeStatus(id, 1);
        return success ? Result.success(true, "上架飞行体验项目成功") : Result.error("上架飞行体验项目失败");
    }

    /**
     * 下架飞行体验项目
     *
     * @param id 项目ID
     * @return 下架结果
     */
    @PutMapping("/unpublish/{id}")
    public Result<Boolean> unpublish(@PathVariable Long id) {
        log.info("管理员下架飞行体验项目：id={}", id);
        boolean success = flightExperienceService.changeStatus(id, 0);
        return success ? Result.success(true, "下架飞行体验项目成功") : Result.error("下架飞行体验项目失败");
    }
} 