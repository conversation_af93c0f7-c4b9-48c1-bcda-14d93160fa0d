package org.example.controller.admin;

import org.example.entity.SystemConfig;
import org.example.service.SystemConfigService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 */
@RestController
@RequestMapping("/admin/system/config")
public class SystemConfigController {
	
    @Autowired
    private SystemConfigService systemConfigService;
    
    /**
     * 获取配置值
     */
    @GetMapping("/get/{key}")
    public Result<String> getConfigValue(@PathVariable String key) {
        String value = systemConfigService.getConfigValue(key);
        return Result.success(value);
    }
    
    /**
     * 设置配置值
     */
    @PostMapping("/set")
    public Result<Boolean> setConfigValue(
            @RequestParam String key, 
            @RequestParam String value, 
            @RequestParam(required = false) String description) {
        
        boolean success = systemConfigService.setConfigValue(key, value, description);
        if (success) {
            return Result.success(true, "设置配置成功");
        } else {
            return Result.error("设置配置失败");
        }
    }
    
    /**
     * 获取预约功能状态
     */
    @GetMapping("/appointment/status")
    public Result<Boolean> getAppointmentStatus() {
        boolean enabled = systemConfigService.isAppointmentEnabled();
        return Result.success(enabled);
    }
    
    /**
     * 设置预约功能状态
     */
    @PostMapping("/appointment/status")
    public Result<Boolean> setAppointmentStatus(@RequestParam boolean enabled) {
        boolean success = systemConfigService.setAppointmentEnabled(enabled);
        if (success) {
            String status = enabled ? "开启" : "关闭";
            return Result.success(true, "成功" + status + "预约功能");
        } else {
            return Result.error("设置预约功能状态失败");
        }
    }
    
    /**
     * 获取所有系统配置
     */
    @GetMapping("/list")
    public Result<List<SystemConfig>> listAllConfigs() {
        List<SystemConfig> configs = systemConfigService.list();
        return Result.success(configs);
    }
    
    /**
     * 获取系统状态概览
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 获取预约功能状态
        boolean appointmentEnabled = systemConfigService.isAppointmentEnabled();
        status.put("appointmentEnabled", appointmentEnabled);
        
        // 可以添加其他系统状态
        
        return Result.success(status);
    }
} 