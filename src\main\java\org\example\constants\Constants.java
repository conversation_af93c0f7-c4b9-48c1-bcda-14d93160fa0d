package org.example.constants;

public class Constants {

    // 飞行报告状态
    public static final String REPORT_STATUS_DRAFT = "DRAFT";
    public static final String REPORT_STATUS_SUBMITTED = "SUBMITTED";
    public static final String REPORT_STATUS_APPROVED = "APPROVED";

    // 预约状态
    public static final String APPOINTMENT_STATUS_PENDING = "PENDING";
    public static final String APPOINTMENT_STATUS_CANCELLED = "CANCELLED";
    public static final String APPOINTMENT_STATUS_CONFIRMED = "CONFIRMED";
    public static final String APPOINTMENT_STATUS_COMPLETED = "COMPLETED";

    // 订单状态
    public static final String ORDER_STATUS_UNPAID = "UNPAID";
    public static final String ORDER_STATUS_PAID = "PAID";
    public static final String ORDER_STATUS_CANCELLED = "CANCELLED";
    public static final String ORDER_STATUS_REFUNDED = "REFUNDED";

    // 时间相关
    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int DEFAULT_CURRENT_PAGE = 1;

    // 学员类型
    public static final String STUDENT_TYPE_SPORT_LICENSE = "SPORT_LICENSE";  // 运动类执照学员
    public static final String STUDENT_TYPE_PRIVATE_LICENSE = "PRIVATE_LICENSE";  // 私用驾驶员执照学员
    public static final String STUDENT_TYPE_SPORT_INSTRUCTOR = "SPORT_INSTRUCTOR";  // 运动类教员学员
    
    // 默认课时
    public static final double DEFAULT_HOURS_SPORT_LICENSE = 30.0;  // 运动类执照默认总课时
    public static final double DEFAULT_HOURS_PRIVATE_LICENSE = 40.0;  // 私用驾驶员执照默认总课时
    public static final double DEFAULT_HOURS_SPORT_INSTRUCTOR = 150.0;  // 运动类教员默认总课时
    
    // 时间段用途
    public static final String TIME_SLOT_PURPOSE_ALL = "ALL";  // 可用于体验和训练
    public static final String TIME_SLOT_PURPOSE_EXPERIENCE_ONLY = "EXPERIENCE_ONLY";  // 仅用于体验
    public static final String TIME_SLOT_PURPOSE_TRAINING_ONLY = "TRAINING_ONLY";  // 仅用于训练
    
    // 时间段状态
    public static final int TIME_SLOT_STATUS_AVAILABLE = 1;  // 可用
    public static final int TIME_SLOT_STATUS_UNAVAILABLE = 0;  // 不可用
}
