package org.example.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.example.entity.Appointment;
import org.example.entity.TimeSlot;
import org.example.mapper.AppointmentMapper;
import org.example.service.TimeSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 过期时间段清理定时任务
 */
@Component
@Slf4j
public class TimeSlotCleanupTask {

    @Autowired
    private TimeSlotService timeSlotService;
    
    @Autowired
    private AppointmentMapper appointmentMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 每天凌晨2点执行清理过期时间段
     * 仅保留当前日期之后的时间段
     * 在删除时间段之前，会先处理关联的预约记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredTimeSlots() {
        log.info("开始执行过期时间段清理任务");
        
        LocalDate today = LocalDate.now();
        
        try {
            // 创建查询条件：查找所有日期在今天之前的时间段
            LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(TimeSlot::getDate, today);
            
            // 先获取所有要删除的时间段
            List<TimeSlot> expiredTimeSlots = timeSlotService.list(wrapper);
            
            if (expiredTimeSlots.isEmpty()) {
                log.info("没有找到过期的时间段，无需清理");
                return;
            }
            
            log.info("找到 {} 个过期时间段需要清理", expiredTimeSlots.size());
            
            int successCount = 0;
            int failCount = 0;
            
            // 逐个处理每个过期时间段
            for (TimeSlot timeSlot : expiredTimeSlots) {
                try {
                    // 使用事务处理单个时间段的清理
                    cleanupSingleTimeSlot(timeSlot);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    log.error("清理时间段ID {} 时发生错误: {}", timeSlot.getId(), e.getMessage());
                }
            }
            
            log.info("过期时间段清理完成，成功: {}，失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("清理过期时间段时发生错误", e);
        }
    }
    
    /**
     * 清理单个时间段及其关联的预约记录
     * 
     * @param timeSlot 要清理的时间段
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanupSingleTimeSlot(TimeSlot timeSlot) {
        log.info("开始清理时间段ID: {}, 日期: {}", timeSlot.getId(), timeSlot.getDate());
        
        try {
            // 使用直接SQL更新语句，将关联预约的time_slot_id设置为null
            String updateSql = "UPDATE appointment SET time_slot_id = NULL, status = 'COMPLETED' WHERE time_slot_id = ?";
            int updatedCount = jdbcTemplate.update(updateSql, timeSlot.getId());
            
            log.info("已通过SQL直接更新时间段ID {} 的 {} 个关联预约", timeSlot.getId(), updatedCount);
            
            // 确保所有关联预约都已更新
            LambdaQueryWrapper<Appointment> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(Appointment::getTimeSlotId, timeSlot.getId());
            long remainingCount = appointmentMapper.selectCount(checkWrapper);
            
            if (remainingCount > 0) {
                throw new RuntimeException("仍有 " + remainingCount + " 个预约记录关联到时间段ID " + timeSlot.getId());
            }
            
            // 删除时间段
            boolean deleted = timeSlotService.removeById(timeSlot.getId());
            
            if (!deleted) {
                throw new RuntimeException("删除时间段ID " + timeSlot.getId() + " 失败");
            }
            
            log.info("成功删除时间段ID: {}", timeSlot.getId());
        } catch (Exception e) {
            log.error("清理时间段ID {} 时发生SQL错误: {}", timeSlot.getId(), e.getMessage());
            throw e;
        }
    }
} 