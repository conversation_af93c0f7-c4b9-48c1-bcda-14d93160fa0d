package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.AdminUser;
import org.example.entity.User;
import org.example.service.AdminUserService;
import org.example.service.UserService;
import org.example.utils.JwtUtil;
import org.example.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员用户控制器
 */
@RestController
@RequestMapping("/admin")
public class AdminUserController {

    private static final Logger logger = LoggerFactory.getLogger(AdminUserController.class);
    
    @Autowired
    private AdminUserService adminUserService;
    
    @Autowired
    private UserService userService;

    /**
     * 管理员登录
     * 
     * @param loginForm 登录表单
     *                  - username: 用户名
     *                  - password: 密码
     * @return 管理员信息（不含密码）和token，登录成功后会更新最后登录时间
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginForm) {
        String username = loginForm.get("username");
        String password = loginForm.get("password");
        
        if (username == null || password == null) {
            return Result.error("用户名和密码不能为空");
        }
        
        AdminUser adminUser = adminUserService.login(username, password);
        if (adminUser != null) {
            if (adminUser.getStatus() != null && adminUser.getStatus() != 1) {
                return Result.error("账号已被禁用，请联系系统管理员");
            }
            
            // 创建一个新的AdminUser对象，只设置ID和最后登录时间，避免重复加密密码
            AdminUser updateUser = new AdminUser();
            updateUser.setId(adminUser.getId());
            updateUser.setLastLoginTime(LocalDateTime.now());
            // 密码设为null，这样updateAdmin方法不会重新加密
            updateUser.setPassword(null);
            adminUserService.updateAdmin(updateUser);
            
            // 生成token
            String token = JwtUtil.createToken(adminUser.getId(), adminUser.getUsername(), "admin");
            logger.info("生成的token: [{}], 长度: {}", token, token.length());
            
            // 构建返回数据，过滤敏感信息
            Map<String, Object> result = new HashMap<>();
            adminUser.setPassword(null); // 清除密码
            result.put("adminInfo", adminUser);
            result.put("token", token);
            
            // 成功响应使用code=1
            return Result.success(result, "登录成功");
        } else {
            return Result.error("用户名或密码错误");
        }
    }

    /**
     * 测试接口 - 需要JWT授权
     * 用于测试JWT授权是否正常工作
     */
    @GetMapping("/test-auth")
    public Result<Map<String, Object>> testAuth(jakarta.servlet.http.HttpServletRequest request) {
        // 从请求属性中获取JWT解析出的信息
        Object userId = request.getAttribute("userId");
        Object username = request.getAttribute("username");
        Object role = request.getAttribute("role");
        
        Map<String, Object> data = new HashMap<>();
        data.put("userId", userId);
        data.put("username", username);
        data.put("role", role);
        data.put("time", LocalDateTime.now().toString());
        
        return Result.success(data, "授权验证成功");
    }
    
    /**
     * 创建管理员
     * 
     * @param adminUser 管理员信息
     *                  - username: 用户名
     *                  - password: 密码
     *                  - status: 状态（可选）
     * @return 新创建的管理员信息，自动检查用户名是否已存在
     */
    @PostMapping("/create")
    public Result<AdminUser> createAdmin(@RequestBody AdminUser adminUser) {
        // 检查用户名是否已存在
        AdminUser existing = adminUserService.findByUsername(adminUser.getUsername());
        if (existing != null) {
            return Result.error("用户名已存在");
        }
        
        boolean success = adminUserService.createAdmin(adminUser);
        if (success) {
            // 查询新创建的管理员信息并返回
            AdminUser newAdmin = adminUserService.findByUsername(adminUser.getUsername());
            return Result.success(newAdmin);
        } else {
            return Result.error("创建管理员失败");
        }
    }

    /**
     * 更新管理员信息
     * 
     * @param adminUser 管理员信息
     *                  - id: 管理员ID（必填）
     *                  - username: 用户名（可选）
     *                  - password: 密码（可选）
     *                  - status: 状态（可选）
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Boolean> updateAdmin(@RequestBody AdminUser adminUser) {
        if (adminUser.getId() == null) {
            return Result.error("管理员ID不能为空");
        }
        boolean success = adminUserService.updateAdmin(adminUser);
        return success ? Result.success(true) : Result.error("更新管理员信息失败");
    }

    /**
     * 分页查询管理员列表
     * 
     * @param current 当前页码（默认1）
     * @param size 每页记录数（默认10）
     * @param username 用户名（可选，模糊查询）
     * @return 管理员列表及分页信息
     */
    @GetMapping("/page")
    public Result<Page<AdminUser>> pageAdmins(
            @RequestParam(value = "current", defaultValue = "1") int current,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "username", required = false) String username) {
        
        Page<AdminUser> page = adminUserService.pageAdmins(current, size, username);
        return Result.success(page);
    }

    /**
     * 获取管理员详情
     * 
     * @param id 管理员ID（路径参数）
     * @return 管理员详细信息
     */
    @GetMapping("/detail/{id}")
    public Result<AdminUser> getAdminDetail(@PathVariable Long id) {
        AdminUser adminUser = adminUserService.getAdminDetail(id);
        return adminUser != null ? Result.success(adminUser) : Result.error("管理员不存在");
    }

    /**
     * 删除管理员
     * 
     * @param id 管理员ID（路径参数）
     * @return 操作结果
     */
    @DeleteMapping("/remove/{id}")
    public Result<Boolean> removeAdmin(@PathVariable Long id) {
        boolean success = adminUserService.removeAdmin(id);
        return success ? Result.success(true) : Result.error("删除管理员失败");
    }
    
    /**
     * 查询所有用户（分页）
     * 管理员权限接口：获取所有用户列表
     * 
     * @param current 当前页码（默认1）
     * @param size 每页记录数（默认10）
     * @param role 用户角色（可选）
     * @param username 用户名（可选，模糊查询）
     * @return 用户列表及分页信息
     */
    @GetMapping("/users")
    public Result<Page<User>> getAllUsers(
            @RequestParam(value = "current", defaultValue = "1") int current,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "role", required = false) String role,
            @RequestParam(value = "username", required = false) String username) {
        
        Page<User> page = userService.pageUsers(current, size, role, username);
        return Result.success(page);
    }
    
    /**
     * 获取用户详情
     * 管理员权限接口
     * 
     * @param id 用户ID（路径参数）
     * @return 用户详细信息
     */
    @GetMapping("/users/{id}")
    public Result<User> getUserDetail(@PathVariable Long id) {
        User user = userService.getUserDetail(id);
        return user != null ? Result.success(user) : Result.error("用户不存在");
    }
    
    /**
     * 更新用户状态
     * 管理员权限接口：禁用/启用用户
     * 
     * @param id 用户ID（路径参数）
     * @param status 状态值
     * @return 操作结果
     */
    @PutMapping("/users/{id}/status")
    public Result<Boolean> updateUserStatus(
            @PathVariable Long id, 
            @RequestParam Integer status) {
        boolean success = userService.updateUserStatus(id, status);
        return success ? Result.success(true) : Result.error("更新用户状态失败");
    }
} 