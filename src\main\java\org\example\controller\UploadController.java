package org.example.controller;


import org.example.service.FileService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @Description:
 * @Auther: 苏紫陌
 * @Date: 2025/6/22 21:28
 */

@RestController
@RequestMapping("/upload")
public class UploadController {
	
	@Autowired
	private FileService fileService;
	
	/**
	 * 上传图片
	 */
	@PostMapping
	public Result upload(MultipartFile imgFile) throws IOException {
		try {
			if (imgFile == null || imgFile.isEmpty()) {
				return Result.error("文件为空");
			}
			
			String imageUrl = fileService.upload(imgFile);
			return Result.success(imageUrl);
		} catch (Exception e) {
			e.printStackTrace();
			return Result.error("上传失败: " + e.getMessage());
		}
	}

}
