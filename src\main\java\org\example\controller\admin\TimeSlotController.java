package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.constants.Constants;
import org.example.dto.BatchTimeSlotDTO;
import org.example.dto.TimeSlotDTO;
import org.example.entity.TimeSlot;
import org.example.service.SystemConfigService;
import org.example.service.TimeSlotService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 时间段管理控制器
 */
@RestController
@RequestMapping("/timeSlot")
public class TimeSlotController {

    @Autowired
    private TimeSlotService timeSlotService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    /**
     * 添加时间段
     */
    @PostMapping("/add")
    public Result<Boolean> addTimeSlot(@RequestBody TimeSlotDTO timeSlotDTO) {
        if (timeSlotDTO.getStartTime() == null || timeSlotDTO.getEndTime() == null || timeSlotDTO.getDate() == null) {
            return Result.error("开始时间、结束时间和日期不能为空");
        }
		
        boolean success = timeSlotService.addTimeSlot(timeSlotDTO);
        if (success) {
            return Result.success(true, "添加时间段成功");
        } else {
            return Result.error("添加时间段失败，可能与已有时间段冲突");
        }
    }
    
    /**
     * 更新时间段
     */
    @PutMapping("/update")
    public Result<Boolean> updateTimeSlot(@RequestBody TimeSlotDTO timeSlotDTO) {
        if (timeSlotDTO.getId() == null) {
            return Result.error("时间段ID不能为空");
        }
        
        // 获取原时间段信息，检查已预约数量是否超过新设置的容量
        TimeSlotDTO originalDTO = timeSlotService.getTimeSlotDTO(timeSlotDTO.getId());
        if (originalDTO == null) {
            return Result.error("时间段不存在");
        }
        
        if (timeSlotDTO.getExperienceCapacity() != null && 
            originalDTO.getExperienceBookedCount() != null && 
            timeSlotDTO.getExperienceCapacity() < originalDTO.getExperienceBookedCount()) {
            return Result.error("体验预约容量不能小于已预约数量: " + originalDTO.getExperienceBookedCount());
        }
        
        if (timeSlotDTO.getTrainingCapacity() != null && 
            originalDTO.getTrainingBookedCount() != null && 
            timeSlotDTO.getTrainingCapacity() < originalDTO.getTrainingBookedCount()) {
            return Result.error("训练预约容量不能小于已预约数量: " + originalDTO.getTrainingBookedCount());
        }
        
        boolean success = timeSlotService.updateTimeSlot(timeSlotDTO);
        if (success) {
            return Result.success(true, "更新时间段成功");
        } else {
            return Result.error("更新时间段失败，可能与已有时间段冲突或ID不存在");
        }
    }
    
    /**
     * 删除时间段
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteTimeSlot(@PathVariable Long id) {
		
        boolean success = timeSlotService.removeTimeSlot(id);
        if (success) {
            return Result.success(true, "删除时间段成功");
        } else {
            return Result.error("删除时间段失败，ID可能不存在");
        }
    }
    
    /**
     * 获取时间段详情
     */
    @GetMapping("/get/{id}")
    public Result<TimeSlotDTO> getTimeSlot(@PathVariable Long id) {
        TimeSlotDTO timeSlotDTO = timeSlotService.getTimeSlotDTO(id);
        if (timeSlotDTO != null) {
            return Result.success(timeSlotDTO);
        } else {
            return Result.error("时间段不存在");
        }
    }
    
    /**
     * 分页查询时间段
     */
    @GetMapping("/page")
    public Result<Page<TimeSlotDTO>> pageTimeSlots(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer status) {
        Page<TimeSlotDTO> page = timeSlotService.pageTimeSlots(current, size, status);
        return Result.success(page);
    }
    
    /**
     * 批量创建时间段
     */
    @PostMapping("/batchCreate")
    public Result<Integer> batchCreateTimeSlots(@RequestBody BatchTimeSlotDTO batchDTO) {
        if (batchDTO.getStartDate() == null || batchDTO.getEndDate() == null) {
            return Result.error("开始日期和结束日期不能为空");
        }
        
        if (batchDTO.getTemplates() == null || batchDTO.getTemplates().isEmpty()) {
            return Result.error("时间段模板不能为空");
        }
        
        // 验证模板中的容量设置
        for (int i = 0; i < batchDTO.getTemplates().size(); i++) {
            var template = batchDTO.getTemplates().get(i);
            
            // 验证容量字段
            if (template.getExperienceCapacity() != null && template.getExperienceCapacity() < 0) {
                return Result.error("模板" + (i+1) + "：体验预约容量不能小于0");
            }
            
            if (template.getTrainingCapacity() != null && template.getTrainingCapacity() < 0) {
                return Result.error("模板" + (i+1) + "：训练预约容量不能小于0");
            }
            
            // 设置默认容量值
            if (template.getExperienceCapacity() == null) {
                template.setExperienceCapacity(1);
            }
            if (template.getTrainingCapacity() == null) {
                template.setTrainingCapacity(1);
            }
        }
        
        int count = timeSlotService.batchCreateTimeSlots(batchDTO);
        if (count > 0) {
            return Result.success(count, "成功创建" + count + "个时间段");
        } else {
            return Result.error("创建时间段失败，请检查参数");
        }
    }
	
    /**
     * 批量更新时间段状态
     */
    @PutMapping("/batchUpdateStatus")
    public Result<Integer> batchUpdateStatus(
            @RequestParam List<Long> ids,
            @RequestParam Integer status) {
        
        if (ids == null || ids.isEmpty()) {
            return Result.error("时间段ID列表不能为空");
        }
        
        if (status == null || (status != Constants.TIME_SLOT_STATUS_AVAILABLE && status != Constants.TIME_SLOT_STATUS_UNAVAILABLE)) {
            return Result.error("状态值无效");
        }
        
        int count = timeSlotService.batchUpdateStatus(ids, status);
        if (count > 0) {
            return Result.success(count, "成功更新" + count + "个时间段状态");
        } else {
            return Result.error("更新时间段状态失败");
        }
    }
    
    /**
     * 设置某一天所有时间段状态
     */
    @PostMapping("/setDateStatus")
    public Result<Boolean> setDateStatus(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam Integer status,
            @RequestParam(required = false) String description) {
        
        boolean success = timeSlotService.setDateSlotsStatus(date, status, description);
        String statusText = status == 1 ? "开启" : "关闭";
        
        if (success) {
            return Result.success(true, "成功" + statusText + date + "的所有时间段");
        } else {
            return Result.error("设置失败");
        }
    }
    
    /**
     * 批量设置日期范围内所有时间段状态
     */
    @PostMapping("/setDateRangeStatus")
    public Result<Boolean> setDateRangeStatus(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam Integer status,
            @RequestParam(required = false) String description) {
        
        boolean success = timeSlotService.setDateRangeSlotsStatus(startDate, endDate, status, description);
        String statusText = status == 1 ? "开启" : "关闭";
        
        if (success) {
            return Result.success(true, "成功" + statusText + startDate + "至" + endDate + "的所有时间段");
        } else {
            return Result.error("设置失败");
        }
    }
    
    /**
     * 创建并设置某一天的时间段状态
     * 如果该日期没有时间段，则创建默认时间段
     */
    @PostMapping("/createAndSetDateStatus")
    public Result<Boolean> createAndSetDateStatus(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam Integer status,
            @RequestParam(required = false) String description) {
        
        boolean success = timeSlotService.createAndSetDateSlots(date, status, description);
        String statusText = status == 1 ? "开启" : "关闭";
        
        if (success) {
            return Result.success(true, "成功创建并" + statusText + date + "的时间段");
        } else {
            return Result.error("创建或设置失败");
        }
    }
	
} 