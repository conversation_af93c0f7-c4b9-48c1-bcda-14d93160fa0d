package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.example.entity.User;
import org.example.mapper.UserMapper;
import org.example.service.UserService;
import org.example.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.alibaba.fastjson.JSONObject;

import jakarta.servlet.http.HttpServletRequest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户服务实现类
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private RestTemplate restTemplate;
	
	@Autowired
	private UserMapper userMapper;
	
    
    @Value("${wx.appid}")
    private String appId;
    
    @Value("${wx.secret}")
    private String appSecret;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    @Override
    public String getWxOpenid(String code) {
        try {
            // 检查是否为开发环境，如果是且code是特定值，返回测试openid
            if ("dev".equals(activeProfile) && (code.startsWith("test_") || code.equals("123456"))) {
                String testOpenid = "test_openid_" + System.currentTimeMillis();
                log.info("开发环境测试模式，返回测试openid: {}", testOpenid);
                return testOpenid;
            }
            
            // 检查appId和appSecret是否已配置
            if (appId == null || appId.isEmpty() || appSecret == null || appSecret.isEmpty()) {
                log.error("微信小程序配置错误: appId={}, appSecret={}", 
                          appId != null ? appId : "未配置", 
                          appSecret != null ? "已配置" : "未配置");
                
                // 开发环境下，如果配置错误也返回测试openid
                if ("dev".equals(activeProfile)) {
                    String testOpenid = "test_openid_" + System.currentTimeMillis();
                    log.info("开发环境，配置错误但返回测试openid: {}", testOpenid);
                    return testOpenid;
                }
                return null;
            }
            
            log.info("开始调用微信登录凭证校验接口，code={}, appId={}", code, appId);
            
            // 微信登录凭证校验接口
            String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId +
                    "&secret=" + appSecret +
                    "&js_code=" + code +
                    "&grant_type=authorization_code";
            
            log.info("请求微信API: {}", url);
            
            // 使用String类型接收响应，而不是Map
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();
            
            log.info("微信API返回原始结果: {}", responseBody);
            
            // 简单解析返回的JSON字符串
            if (responseBody != null && responseBody.contains("openid")) {
                // 提取openid (简单解析，实际项目中应使用JSON库)
                int openidStart = responseBody.indexOf("\"openid\":\"") + 10;
                int openidEnd = responseBody.indexOf("\"", openidStart);
                
                if (openidStart > 10 && openidEnd > openidStart) {
                    String openid = responseBody.substring(openidStart, openidEnd);
                    log.info("成功获取openid: {}", openid);
                    return openid;
                }
            }
            
            // 检查是否包含错误码
            if (responseBody != null && responseBody.contains("errcode")) {
                log.error("微信API返回错误: {}", responseBody);
            } else {
                log.error("获取微信openid失败，无法解析响应: {}", responseBody);
            }
            
            // 开发环境下，API调用失败也返回测试openid
            if ("dev".equals(activeProfile)) {
                String testOpenid = "test_openid_" + System.currentTimeMillis();
                log.info("开发环境，API调用失败但返回测试openid: {}", testOpenid);
                return testOpenid;
            }
            
            return null;
        } catch (Exception e) {
            log.error("调用微信接口异常: {}", e.getMessage(), e);
            
            // 开发环境下，发生异常也返回测试openid
            if ("dev".equals(activeProfile)) {
                String testOpenid = "test_openid_" + System.currentTimeMillis();
                log.info("开发环境，发生异常但返回测试openid: {}", testOpenid);
                return testOpenid;
            }
            
            return null;
        }
    }
    
    @Override
    public User getUserByOpenid(String openid) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getOpenid, openid);
        return getOne(wrapper);
    }
    
    @Override
    public boolean registerWxUser(User user) {
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreateTime(now);
        user.setUpdateTime(now);
		
        return save(user);
    }
    
    @Override
    public boolean updateUserInfo(User user) {
        // 设置更新时间
        user.setUpdateTime(LocalDateTime.now());
        return updateById(user);
    }
    
    @Override
    public User getUserById(Long id) {
        return getById(id);
    }
    
    @Override
    public Long getCurrentUserId() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = request.getHeader("Authorization");
            
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                return JwtUtil.getUserId(token);
            }
            return null;
        } catch (Exception e) {
            log.error("获取当前用户ID异常", e);
            return null;
        }
    }
    
    @Override
    public int countTotalUsers() {
	    // 统计所有用户数量
	    return userMapper.selectCount(null).intValue();
    }
	
	@Override
	@Transactional
	public boolean saveUser(User user) {
		if (user.getId() == null) {
			user.setCreateTime(LocalDateTime.now());
			user.setUpdateTime(LocalDateTime.now());
			return userMapper.insert(user) > 0;
		} else {
			user.setUpdateTime(LocalDateTime.now());
			return userMapper.updateById(user) > 0;
		}
	}
	
	@Override
	public Page<User> pageUsers(int current, int size, String role, String username) {
		Page<User> page = new Page<>(current, size);
		LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(role != null, User::getRole, role)
				.like(username != null, User::getUsername, username)
				.orderByDesc(User::getCreateTime);
		return userMapper.selectPage(page, wrapper);
	}
	
	@Override
	public User getUserDetail(Long id) {
		return userMapper.selectById(id);
	}
	
	@Override
	@Transactional
	public boolean updateUserStatus(Long userId, Integer status) {
		User user = new User();
		user.setId(userId);
		user.setStatus(status);
		user.setUpdateTime(LocalDateTime.now());
		return userMapper.updateById(user) > 0;
	}
	
	@Override
	@Transactional
	public boolean removeUser(Long id) {
		return userMapper.deleteById(id) > 0;
	}
	
	@Override
	@Transactional
	public boolean updateUser(User user) {
		if (user.getId() == null) {
			return false;
		}
		user.setUpdateTime(LocalDateTime.now());
		return userMapper.updateById(user) > 0;
	}
	
	@Override
	public User getUserByUsername(String username) {
		LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(User::getUsername, username);
		return userMapper.selectOne(wrapper);
	}
	
    @Override
    public User getUserByPhone(String phone) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        return userMapper.selectOne(wrapper);
    }
    
    @Override
    @Transactional
    public boolean registerPhoneUser(User user) {
        // 设置创建时间
        user.setCreateTime(LocalDateTime.now());
        
        // 保存用户
        return this.save(user);
    }
    
    @Override
    public String getWxPhoneNumber(String phoneCode) {
        try {
            // 检查是否为开发环境
            if ("dev".equals(activeProfile)) {
                // 开发环境返回测试手机号
                return "13800138000";
            }
            
            // 检查appId和appSecret是否已配置
            if (appId == null || appId.isEmpty() || appSecret == null || appSecret.isEmpty()) {
                log.error("微信小程序配置错误: appId={}, appSecret={}", 
                          appId != null ? appId : "未配置", 
                          appSecret != null ? "已配置" : "未配置");
                return null;
            }
            
            // 获取接口调用凭证access_token
            String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;
            ResponseEntity<String> accessTokenResponse = restTemplate.getForEntity(accessTokenUrl, String.class);
            JSONObject accessTokenJson = JSONObject.parseObject(accessTokenResponse.getBody());
            
            if (!accessTokenJson.containsKey("access_token")) {
                log.error("获取微信access_token失败: {}", accessTokenJson);
                return null;
            }
            
            String accessToken = accessTokenJson.getString("access_token");
            
            // 调用微信接口获取手机号
            String phoneUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
            JSONObject requestBody = new JSONObject();
            requestBody.put("code", phoneCode);
            
            ResponseEntity<String> phoneResponse = restTemplate.postForEntity(phoneUrl, requestBody, String.class);
            JSONObject phoneJson = JSONObject.parseObject(phoneResponse.getBody());
            
            if (phoneJson.getIntValue("errcode") != 0) {
                log.error("获取微信手机号失败: {}", phoneJson);
                return null;
            }
            
            // 解析手机号
            JSONObject phoneInfo = phoneJson.getJSONObject("phone_info");
            if (phoneInfo != null && phoneInfo.containsKey("phoneNumber")) {
                return phoneInfo.getString("phoneNumber");
            }
            
            return null;
        } catch (Exception e) {
            log.error("获取微信手机号异常", e);
            return null;
        }
    }
    
    @Override
    @Transactional
    public User mergeUsers(User mainUser, User secondaryUser) {
        // 以主账号为准，将次要账号的信息合并到主账号
        if (mainUser == null) {
            return secondaryUser;
        }
        
        if (secondaryUser == null) {
            return mainUser;
        }
        
        // 确保手机号不为空
        if (StringUtils.isEmpty(mainUser.getPhone()) && !StringUtils.isEmpty(secondaryUser.getPhone())) {
            mainUser.setPhone(secondaryUser.getPhone());
        }
        
        // 确保openid不为空
        if (StringUtils.isEmpty(mainUser.getOpenid()) && !StringUtils.isEmpty(secondaryUser.getOpenid())) {
            mainUser.setOpenid(secondaryUser.getOpenid());
        }
        
        // 更新主账号
        mainUser.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(mainUser);
        
        // 禁用次要账号
        secondaryUser.setStatus(0);  // 0表示禁用
        secondaryUser.setUpdateTime(LocalDateTime.now());
        userMapper.updateById(secondaryUser);
        
        return mainUser;
    }
} 