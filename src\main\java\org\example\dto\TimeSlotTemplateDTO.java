package org.example.dto;

import lombok.Data;

import java.time.LocalTime;

/**
 * 时间段模板DTO
 */
@Data
public class TimeSlotTemplateDTO {
    /**
     * 开始时间
     */
    private LocalTime startTime;
    
    /**
     * 结束时间
     */
    private LocalTime endTime;
    
    /**
     * 用途:ALL,EXPERIENCE_ONLY,TRAINING_ONLY
     */
    private String purpose;
    
    /**
     * 体验预约容量
     */
    private Integer experienceCapacity;
    
    /**
     * 训练预约容量
     */
    private Integer trainingCapacity;
    
    /**
     * 时间段描述
     */
    private String description;
} 