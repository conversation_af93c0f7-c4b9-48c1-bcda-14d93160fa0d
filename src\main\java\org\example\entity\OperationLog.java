package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("operation_log")
public class OperationLog extends BaseEntity {

    @TableField("admin_id")
    private Long adminId;

    @TableField("operation_type")
    private String operationType;

    @TableField("operation_content")
    private String operationContent;

    @TableField("ip")
    private String ip;

    @TableField("create_time")
    private LocalDateTime createTime;
} 