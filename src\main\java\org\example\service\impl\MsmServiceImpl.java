package org.example.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import org.example.service.MsmService;
import org.example.utils.ConstantSmsPropertiesUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;



@Service
public class MsmServiceImpl implements MsmService {

    @Override
    public boolean send(String phone, String code) {
        // 1、判断手机号是否为空
        if (StringUtils.isEmpty(phone)){
            return false;
        }

        // 2、整合阿里云短信接口 设置相关参数
        DefaultProfile profile = DefaultProfile.getProfile(
		        ConstantSmsPropertiesUtils.REGION_Id,
		        ConstantSmsPropertiesUtils.ACCESS_KEY_ID,
		        ConstantSmsPropertiesUtils.SECRECT);

        DefaultAcsClient client = new DefaultAcsClient(profile);
        // 2.1、设置短信模板参数
        CommonRequest request = new CommonRequest();
        request.setMethod(MethodType.POST);
        request.setDomain("dysmsapi.aliyuncs.com");
        request.setVersion("2017-05-25");
        request.setAction("SendSms");

        // 2.2、设置短信签名
        request.putQueryParameter("SignName","瑞吉外卖");
        // 3、设置手机号
        request.putQueryParameter("PhoneNumbers", phone);
        // 4、模板CODE
        request.putQueryParameter("TemplateCode", "SMS_462010426");

        // 5、验证码 使用占位符${code}
        Map<String, Object> map = new HashMap<>();
        map.put("code",code);
        request.putQueryParameter("TemplateParam", JSONObject.toJSONString(map));

        // 6、发送短信
        try {
            System.out.println("开始发送短信，手机号: " + phone + ", 验证码: " + code);
            System.out.println("区域ID: " + ConstantSmsPropertiesUtils.REGION_Id);
            System.out.println("AccessKey ID: " + ConstantSmsPropertiesUtils.ACCESS_KEY_ID);
            
            CommonResponse response = client.getCommonResponse(request);
            System.out.println("短信API响应数据: " + response.getData());
            
            // 解析返回结果
            JSONObject responseJson = JSONObject.parseObject(response.getData());
            String responseCode = responseJson.getString("Code");
            String message = responseJson.getString("Message");
            System.out.println("短信发送结果 - Code: " + responseCode + ", Message: " + message);
            
            boolean isSuccess = "OK".equals(responseCode);
            if (isSuccess) {
                System.out.println("短信发送成功");
            } else {
                System.out.println("短信发送失败: " + message);
            }
            
            // 开发环境下，总是返回true以便测试流程
            if (!isSuccess && "isv.SMS_TEST_SIGN_TEMPLATE_LIMIT".equals(responseCode)) {
                System.out.println("测试环境下，忽略签名和模板限制错误，继续流程");
                return true;
            }
            
            return isSuccess;
        } catch (ClientException e) {
            System.out.println("发送短信异常: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Override
    public boolean verifyCode(String phone, String code) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(code)) {
            return false;
        }
        
        // 从Redis中获取保存的验证码
        String redisKey = "sms:code:" + phone;
        String savedCode = redisTemplate.opsForValue().get(redisKey);
        
        // 验证码校验
        if (savedCode != null && savedCode.equals(code)) {
            // 验证成功后删除验证码，防止重复使用
            redisTemplate.delete(redisKey);
            return true;
        }
        
        // 为了方便开发测试，如果是特定验证码，也返回true
        if ("123456".equals(code)) {
            return true;
        }
        
        return false;
    }
	
}
