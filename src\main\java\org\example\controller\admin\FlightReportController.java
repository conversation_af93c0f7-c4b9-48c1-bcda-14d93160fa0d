package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.FlightReport;
import org.example.service.FlightReportService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞行报告控制器
 */
@RestController
@RequestMapping("/flight-reports")
public class FlightReportController {

    @Autowired
    private FlightReportService flightReportService;

    /**
     * 创建飞行报告
     * 
     * @param flightReport 飞行报告信息
     *                    - userCourseId: 学员课时记录ID（可选，如不提供则通过姓名查找）
     *                    - pilotName: 主驾驶姓名（必填）
     *                    - copilotName: 副驾驶姓名（必填）
     *                    - departureAirport: 起飞机场（必填）
     *                    - landingAirport: 降落机场（必填）
     *                    - trainingType: 任务性质（必填）
     *                    - flightDate: 飞行日期（可选，默认当天）
     *                    - startupTime: 开车时间（必填，格式：时:分）
     *                    - takeoffTime: 起飞时间（必填，格式：时:分）
     *                    - landingTime: 着陆时间（必填，格式：时:分）
     *                    - shutdownTime: 关车时间（必填，格式：时:分）
     *                    - landingCount: 着陆次数（必填）
     *                    - totalHours: 飞行总时间（自动计算）
     *                    - reportStatus: 报告状态（可选，默认"未提交"）
     *                    - remarks: 备注
     * @return 创建的飞行报告详情
     * 
     * 业务规则：
     * - 自动计算空中时间（起飞时间到着陆时间）
     * - 自动计算地面时间（开车到起飞 + 着陆到关车）
     * - 自动计算合计时间（空中时间 + 地面时间）
     * - 使用空中时间更新学员已完成课时
     */
    @PostMapping
    public Result<FlightReport> createFlightReport(@RequestBody FlightReport flightReport) {
        try {
            boolean success = flightReportService.createFlightReport(flightReport);
            if (success) {
                // 返回创建后的报告
                FlightReport newReport = flightReportService.getFlightReport(flightReport.getId());
                return Result.success(newReport);
            } else {
                // 返回更具体的错误信息
                return Result.error("创建飞行报告失败：请先创建学员的课时信息！");
            }
        } catch (RuntimeException e) {
            // 捕获特定异常，返回更友好的错误信息
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新飞行报告
     * 
     * @param flightReport 飞行报告信息
     *                    - id: 报告ID（必填）
     *                    - 其他需更新的字段
     * @return 更新后的飞行报告
     */
    @PutMapping
    public Result<FlightReport> updateFlightReport(@RequestBody FlightReport flightReport) {
        if (flightReport.getId() == null) {
            return Result.error("报告ID不能为空");
        }
        
        try {
            boolean success = flightReportService.updateFlightReport(flightReport);
            if (success) {
                FlightReport updatedReport = flightReportService.getFlightReport(flightReport.getId());
                return Result.success(updatedReport);
            } else {
                return Result.error("更新飞行报告失败");
            }
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取飞行报告详情
     * 
     * @param id 报告ID（路径参数）
     * @return 飞行报告详情
     */
    @GetMapping("/{id}")
    public Result<FlightReport> getFlightReport(@PathVariable Long id) {
        FlightReport report = flightReportService.getFlightReport(id);
        return report != null ? Result.success(report) : Result.error("飞行报告不存在");
    }

    /**
     * 分页查询飞行报告（支持多种条件过滤）
     * 
     * @param current 当前页码（默认1）
     * @param size 每页记录数（默认10）
     * @param status 报告状态（可选）
     * @param userCourseId 学员课时记录ID（可选）
     * @param studentName 学员姓名（可选，查询主驾或副驾包含该姓名的报告）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param all 是否查询所有报告（默认false）
     * @return 飞行报告列表及分页信息
     * 
     * 查询逻辑：
     * 1. 如果提供了学员姓名，则查询该学员作为主驾驶或副驾驶的报告
     * 2. 如果没有提供学员姓名但提供了日期，则按日期查询
     * 3. 如果没有提供学员姓名和日期，则默认查询当天的报告
     * 4. 如果明确要查询所有报告，将all参数设为true
     */
    @GetMapping("/page")
    public Result<Page<FlightReport>> pageFlightReports(
            @RequestParam(value = "current", defaultValue = "1") int current,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "userCourseId", required = false) Long userCourseId,
            @RequestParam(value = "studentName", required = false) String studentName,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "all", defaultValue = "false") boolean all) {
        
        Page<FlightReport> page;
        
        // 如果只传了开始日期，默认为当天查询
        if (startDate != null && endDate == null) {
            endDate = startDate;
        }
        
        // 情况1：按学员姓名查询（无论是主驾还是副驾）
        if (studentName != null && !studentName.trim().isEmpty()) {
            page = flightReportService.pageFlightReportsByStudentName(current, size, studentName, status, startDate, endDate);
        }
        // 情况2：查询所有报告（明确指定all=true）
        else if (all) {
            page = flightReportService.pageFlightReports(current, size, status, userCourseId, null, null);
        }
        // 情况3：有日期条件，按日期查询
        else if (startDate != null) {
            page = flightReportService.pageFlightReports(current, size, status, userCourseId, startDate, endDate);
        }
        // 情况4：无条件，默认查询当天
        else {
            LocalDate today = LocalDate.now();
            page = flightReportService.pageFlightReports(current, size, status, userCourseId, today, today);
        }
        
        return Result.success(page);
    }

    /**
     * 删除飞行报告（逻辑删除）
     * 报告将被标记为已删除，但记录仍保留在数据库中
     * 
     * @param id 报告ID（路径参数）
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteReport(@PathVariable Long id) {
        boolean success = flightReportService.deleteReport(id);
        return success ? Result.success(true) : Result.error("删除报告失败");
    }
    
    /**
     * 学员查询个人飞行报告
     * 学员只能查询与自己相关的报告（作为主驾或副驾）
     * 
     * @param studentName 学员姓名（必填）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 飞行报告列表
     */
    @GetMapping("/student")
    public Result<List<FlightReport>> getStudentReports(
            @RequestParam(value = "studentName", required = true) String studentName,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        if (studentName == null || studentName.trim().isEmpty()) {
            return Result.error("学员姓名不能为空");
        }
        
        // 处理日期范围
        if (startDate != null && endDate == null) {
            endDate = startDate;
        }
        
        // 使用分页查询但只取内容列表
        Page<FlightReport> page = flightReportService.pageFlightReportsByStudentName(1, 1000, studentName, null, startDate, endDate);
        return Result.success(page.getRecords());
    }
    
    /**
     * 按飞机和日期查询飞行报告
     * 
     * @param aircraftRegNumber 机号（必填）
     * @param date 飞行日期（必填）
     * @return 飞行报告列表，按开车时间排序
     */
    @GetMapping("/by-aircraft")
    public Result<List<FlightReport>> getFlightReportsByAircraft(
            @RequestParam String aircraftRegNumber,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        if (aircraftRegNumber == null || aircraftRegNumber.trim().isEmpty()) {
            return Result.error("机号不能为空");
        }
        
        if (date == null) {
            return Result.error("飞行日期不能为空");
        }
        
        List<FlightReport> reports = flightReportService.getFlightReportsByAircraftAndDate(aircraftRegNumber, date);
        return Result.success(reports);
    }
	
	
}