package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.dto.FlightExperienceDTO;
import org.example.dto.PaymentResponseDTO;
import org.example.entity.Appointment;
import org.example.entity.FlightExperience;
import org.example.entity.Orders;
import org.example.mapper.AppointmentMapper;
import org.example.mapper.OrdersMapper;
import org.example.service.FlightExperienceService;
import org.example.service.OrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;

/**
 * 订单服务实现类
 */
@Service
public class OrdersServiceImpl implements OrdersService {

    @Autowired
    private OrdersMapper ordersMapper;
    
    @Autowired
    private AppointmentMapper appointmentMapper;
    
    @Autowired
    private FlightExperienceService flightExperienceService;
    
    @Override
    @Transactional
    public Orders createOrder(Orders order) {
        // 生成订单号
        order.setOrderNo(generateOrderNo());
        order.setStatus("UNPAID"); // 默认未支付
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        if (ordersMapper.insert(order) > 0) {
            return order; // 返回带ID的订单
        }
        return null;
    }
    
    @Override
    @Transactional
    public PaymentResponseDTO createProductOrder(Long userId, String productType, Long productId, BigDecimal amount, String productName) {
        // 创建订单
        Orders order = new Orders();
        order.setUserId(userId);
        order.setProductType(productType);
        order.setProductId(productId);
        order.setAmount(amount);
        
        Orders createdOrder = createOrder(order);
        if (createdOrder == null) {
            return null;
        }
        
        // 构建支付响应
        PaymentResponseDTO response = new PaymentResponseDTO();
        response.setOrderNo(createdOrder.getOrderNo());
        response.setAmount(createdOrder.getAmount());
        response.setProductName(productName);
        response.setStatus(createdOrder.getStatus());
        
        // 这里可以集成实际的支付网关，生成支付二维码或支付页面URL
        response.setQrCodeUrl("/payment/qrcode/" + createdOrder.getOrderNo());
        response.setPaymentPageUrl("/payment/page/" + createdOrder.getOrderNo());
        
        return response;
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        // 生成格式: 年月日时分秒 + 4位随机数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String dateTime = LocalDateTime.now().format(formatter);
        
        // 生成4位随机数
        Random random = new Random();
        int randomNum = random.nextInt(10000);
        String randomStr = String.format("%04d", randomNum);
        
        return dateTime + randomStr;
    }

    @Override
    @Transactional
    public boolean payOrder(String orderNo, String paymentMethod, String transactionId) {
        Orders order = getOrderByNo(orderNo);
        if (order == null || !"UNPAID".equals(order.getStatus())) {
            return false;
        }
        
        Orders updateOrder = new Orders();
        updateOrder.setId(order.getId());
        updateOrder.setStatus("PAID");
        updateOrder.setPayTime(LocalDateTime.now());
        updateOrder.setTransactionId(transactionId);
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return ordersMapper.updateById(updateOrder) > 0;
    }

    @Override
    @Transactional
    public boolean cancelOrder(String orderNo) {
        Orders order = getOrderByNo(orderNo);
        if (order == null || !"UNPAID".equals(order.getStatus())) {
            return false;
        }
        
        Orders updateOrder = new Orders();
        updateOrder.setId(order.getId());
        updateOrder.setStatus("CANCELLED");
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return ordersMapper.updateById(updateOrder) > 0;
    }
    
    @Override
    @Transactional
    public boolean refundOrder(String orderNo) {
        Orders order = getOrderByNo(orderNo);
        if (order == null || !"PAID".equals(order.getStatus())) {
            return false;
        }
        
        Orders updateOrder = new Orders();
        updateOrder.setId(order.getId());
        updateOrder.setStatus("REFUNDED");
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return ordersMapper.updateById(updateOrder) > 0;
    }
    
    @Override
    public Orders getOrder(Long id) {
        return ordersMapper.selectById(id);
    }
    
    @Override
    public Orders getOrderByNo(String orderNo) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getOrderNo, orderNo);
        return ordersMapper.selectOne(wrapper);
    }

    @Override
    public String getOrderStatus(String orderNo) {
        Orders order = getOrderByNo(orderNo);
        return order != null ? order.getStatus() : null;
    }

    @Override
    public Page<Orders> pageOrders(int current, int size, String status, Long userId, String productType) {
        Page<Orders> page = new Page<>(current, size);
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(status != null, Orders::getStatus, status)
               .eq(userId != null, Orders::getUserId, userId)
               .eq(productType != null, Orders::getProductType, productType)
               .orderByDesc(Orders::getCreateTime);
        return ordersMapper.selectPage(page, wrapper);
    }
    
    @Override
    public List<Orders> getUserOrders(Long userId) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getUserId, userId)
               .orderByDesc(Orders::getCreateTime);
        return ordersMapper.selectList(wrapper);
    }
    
    @Override
    public Page<Orders> getUserProductTypeOrders(Long userId, String productType, int page, int size) {
        Page<Orders> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Orders::getUserId, userId)
               .eq(Orders::getProductType, productType)
               .orderByDesc(Orders::getCreateTime);
        return ordersMapper.selectPage(pageParam, wrapper);
    }
	
	@Override
	public int countMonthlyOrders(LocalDate startDate, LocalDate endDate) {
		LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
		wrapper.between(Orders::getCreateTime, startDate.atStartOfDay(), endDate.atTime(23, 59, 59));
		wrapper.eq(Orders::getStatus, "PAID"); // 只统计已支付的订单
		return ordersMapper.selectCount(wrapper).intValue();
	}
	
	@Override
	public BigDecimal calculateMonthlyIncome(LocalDate startDate, LocalDate endDate) {
		LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
		wrapper.between(Orders::getCreateTime, startDate.atStartOfDay(), endDate.atTime(23, 59, 59));
		wrapper.eq(Orders::getStatus, "PAID"); // 只统计已支付的订单
		
		// 查询所有符合条件的订单
		List<Orders> orders = ordersMapper.selectList(wrapper);
		
		// 计算总金额
		BigDecimal totalAmount = BigDecimal.ZERO;
		for (Orders order : orders) {
			if (order.getAmount() != null) {
				totalAmount = totalAmount.add(order.getAmount());
			}
		}
		
		return totalAmount;
	}
} 