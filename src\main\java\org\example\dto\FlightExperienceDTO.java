package org.example.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 飞行体验项目DTO
 */
@Data
public class FlightExperienceDTO {
    /**
     * 体验项目ID
     */
    private Long id;
    
    /**
     * 体验项目名称
     */
    private String name;
    
    /**
     * 体验项目描述
     */
    private String description;
    
    /**
     * 飞行时长(小时)
     */
    private Integer duration;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 封面图片URL
     */
    private String coverImage;
    
    /**
     * 状态:0下架,1上架
     */
    private Integer status;
    
    /**
     * 适合人群
     */
    private String suitableFor;
} 