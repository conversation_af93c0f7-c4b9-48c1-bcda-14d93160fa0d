package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.AdminUser;


/**
 * 管理员用户服务接口
 */
public interface AdminUserService {
    
    /**
     * 管理员登录
     * 
     * @param username 用户名
     * @param password 密码
     * @return 管理员信息（登录成功）或null（登录失败）
     */
    AdminUser login(String username, String password);
    
    /**
     * 创建管理员
     * 
     * @param adminUser 管理员信息
     *                  - username: 用户名
     *                  - password: 密码
     *                  - status: 状态（可选）
     * @return 操作结果
     */
    boolean createAdmin(AdminUser adminUser);
    
    /**
     * 更新管理员
     * 
     * @param adminUser 管理员信息
     *                  - id: 管理员ID（必填）
     *                  - username: 用户名（可选）
     *                  - password: 密码（可选）
     *                  - status: 状态（可选）
     *                  - lastLoginTime: 最后登录时间（可选）
     * @return 操作结果
     */
    boolean updateAdmin(AdminUser adminUser);
    
    /**
     * 分页查询管理员
     * 
     * @param current 当前页码
     * @param size 每页记录数
     * @param username 用户名（可选，模糊查询）
     * @return 管理员列表及分页信息
     */
    Page<AdminUser> pageAdmins(int current, int size, String username);
    
    /**
     * 获取管理员详情
     * 
     * @param id 管理员ID
     * @return 管理员详细信息
     */
    AdminUser getAdminDetail(Long id);
    
    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 操作结果
     */
    boolean removeAdmin(Long id);
    
    /**
     * 通过用户名查找管理员
     * 
     * @param username 用户名
     * @return 管理员信息或null（不存在）
     */
    AdminUser findByUsername(String username);
    
    /**
     * 根据ID查找管理员
     * 
     * @param id 管理员ID
     * @return 管理员信息或null（不存在）
     */
    AdminUser findById(Long id);
} 