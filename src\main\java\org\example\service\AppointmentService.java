package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.dto.AppointmentDTO;
import org.example.entity.Appointment;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 预约服务接口
 */
public interface AppointmentService {
    
    /**
     * 创建预约
     */
    boolean createAppointment(Appointment appointment);
	
    /**
     * 获取预约详情
     */
    Appointment getAppointment(Long id);
    
    /**
     * 获取预约详情DTO
     * 
     * @param id 预约ID
     * @return 预约详情DTO
     */
    AppointmentDTO getAppointmentDTO(Long id);
    
    /**
     * 更新预约状态
     */
    boolean updateAppointmentStatus(Long id, String status);
    
    /**
     * 更新预约信息
     * 
     * @param id 预约ID
     * @param appointmentDate 预约日期
     * @param timeSlotId 时间段ID
     * @param aircraftId 飞机ID
     * @param remark 备注
     * @return 是否成功
     */
    boolean updateAppointment(Long id, LocalDate appointmentDate, Long timeSlotId, 
                             Long aircraftId, String remark);
    
    /**
     * 取消预约
     */
    boolean cancelAppointment(Long id);
	
    /**
     * 确认预约
     */
    boolean confirmAppointment(Long id);
    
    /**
     * 完成预约
     */
    boolean completeAppointment(Long id);
    
    /**
     * 查询用户的所有预约
     */
    List<Appointment> getUserAppointments(Long userId);
    
    /**
     * 查询用户的所有预约DTO
     * 
     * @param userId 用户ID
     * @return 预约DTO列表
     */
    List<AppointmentDTO> getUserAppointmentDTOs(Long userId);
	
    
    /**
     * 分页查询预约
     */
    Page<Appointment> pageAppointments(int current, int size, String status, Long userId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 分页查询预约DTO
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param status 状态
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页结果
     */
    Page<AppointmentDTO> pageAppointmentDTOs(int current, int size, String status, Long userId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 检查用户在指定日期和时间段是否已有预约
     * 
     * @param userId 用户ID
     * @param date 日期
     * @param timeSlotId 时间段ID
     * @return 是否已有预约
     */
    boolean hasUserAppointmentAtTimeSlot(Long userId, LocalDate date, Long timeSlotId);
    
    /**
     * 将时间段信息同步到预约记录中
     * 用于在创建或更新预约时，将TimeSlot的信息复制到Appointment中
     * 
     * @param appointment 预约记录
     * @return 是否同步成功
     */
    boolean syncTimeSlotInfo(Appointment appointment);
    
    /**
     * 同步所有现有预约的时间段信息
     * 用于批量迁移数据
     * 
     * @return 成功同步的记录数
     */
    int syncAllAppointmentsTimeSlotInfo();
	
	
	/**
	 * 统计指定日期的预约数量
	 *
	 * @param date 日期
	 * @return 预约数量
	 */
	int countTodayAppointments(LocalDate date);
	
    /**
     * 获取预约分布数据
     * 根据time_slot_purpose字段统计不同类型预约的数量分布
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 预约分布数据列表
     */
    List<Map<String, Object>> getAppointmentDistribution(LocalDate startDate, LocalDate endDate);
	
} 