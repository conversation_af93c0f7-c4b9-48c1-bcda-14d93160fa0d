package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.OperationLog;
import org.example.mapper.OperationLogMapper;
import org.example.service.OperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 操作日志服务实现类
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;
    
    @Override
    public boolean recordLog(OperationLog operationLog) {
        if (operationLog.getCreateTime() == null) {
            operationLog.setCreateTime(LocalDateTime.now());
        }
        return operationLogMapper.insert(operationLog) > 0;
    }
    
    @Override
    public OperationLog getLog(Long id) {
        return operationLogMapper.selectById(id);
    }
    
    @Override
    public Page<OperationLog> pageLog(int current, int size, String module, String operation, Long userId, 
                                     String startTime, String endTime) {
        Page<OperationLog> page = new Page<>(current, size);
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        wrapper.eq(module != null, OperationLog::getOperationType, module)
               .eq(userId != null, OperationLog::getAdminId, userId);
               
        // 处理时间条件
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (startTime != null && !startTime.isEmpty()) {
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            wrapper.ge(OperationLog::getCreateTime, start);
        }
        
        if (endTime != null && !endTime.isEmpty()) {
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);
            wrapper.le(OperationLog::getCreateTime, end);
        }
        
        // 按创建时间降序排序
        wrapper.orderByDesc(OperationLog::getCreateTime);
        
        return operationLogMapper.selectPage(page, wrapper);
    }
    
    @Override
    public int clearExpiredLogs(int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(OperationLog::getCreateTime, expireTime);
        
        return operationLogMapper.delete(wrapper);
    }
} 