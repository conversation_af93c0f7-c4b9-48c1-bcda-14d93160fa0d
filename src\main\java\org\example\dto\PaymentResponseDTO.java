package org.example.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付响应DTO
 */
@Data
public class PaymentResponseDTO {
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 支付二维码URL（如需）
     */
    private String qrCodeUrl;
    
    /**
     * 支付页面URL（如需）
     */
    private String paymentPageUrl;
    
    /**
     * 订单状态
     */
    private String status;
} 