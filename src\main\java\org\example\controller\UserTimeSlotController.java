package org.example.controller;

import org.example.dto.TimeSlotDTO;
import org.example.service.SystemConfigService;
import org.example.service.TimeSlotService;
import org.example.service.UserService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户端时间段查询控制器
 */
@RestController
@RequestMapping("/wx/timeSlot")
public class UserTimeSlotController {

    @Autowired
    private TimeSlotService timeSlotService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 根据日期查询时间段
     */
    @GetMapping("/listByDate")
    public Result<List<TimeSlotDTO>> listTimeSlotsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) String purpose,
            HttpServletRequest request) {
        
        // 检查预约功能是否开启
        if (!systemConfigService.isAppointmentEnabled()) {
            return Result.error("预约功能暂时关闭，请稍后再试");
        }
        
        // 获取当前用户角色
        String role = (String) request.getAttribute("role");
        
        // 获取时间段并根据用户角色进行处理
        List<TimeSlotDTO> timeSlots = timeSlotService.listTimeSlotsByDate(date, purpose);
        processTimeSlotsForUserRole(timeSlots, role);
        
        return Result.success(timeSlots);
    }
    
    /**
     * 根据日期范围查询时间段
     */
    @GetMapping("/listByDateRange")
    public Result<List<TimeSlotDTO>> listTimeSlotsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String purpose,
            HttpServletRequest request) {
        
        // 检查预约功能是否开启
        if (!systemConfigService.isAppointmentEnabled()) {
            return Result.error("预约功能暂时关闭，请稍后再试");
        }
        
        // 获取当前用户角色
        String role = (String) request.getAttribute("role");
        
        // 获取时间段并根据用户角色进行处理
        List<TimeSlotDTO> timeSlots = timeSlotService.listTimeSlotsByDateRange(startDate, endDate, purpose);
        processTimeSlotsForUserRole(timeSlots, role);
        
        return Result.success(timeSlots);
    }
    
    /**
     * 获取当前月份可预约的日期
     */
    @GetMapping("/availableDays")
    public Result<List<LocalDate>> getAvailableDays(
            @RequestParam int year,
            @RequestParam int month,
            @RequestParam(required = false) String purpose,
            HttpServletRequest request) {
        
        // 检查预约功能是否开启
        if (!systemConfigService.isAppointmentEnabled()) {
            return Result.error("预约功能暂时关闭，请稍后再试");
        }
        
        // 获取当前用户角色
        String role = (String) request.getAttribute("role");
        String userPurpose = "NORMAL".equals(role) ? "experience" : "training";
        
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        
        // 根据用户角色确定查询用途
        List<LocalDate> availableDays = timeSlotService.getAvailableDays(startDate, endDate, userPurpose);
        return Result.success(availableDays);
    }
    
    /**
     * 根据用户角色处理时间段数据
     * @param timeSlots 时间段列表
     * @param role 用户角色
     */
    private void processTimeSlotsForUserRole(List<TimeSlotDTO> timeSlots, String role) {
        if (timeSlots == null || timeSlots.isEmpty()) {
            return;
        }
        
        // 根据用户角色计算可用容量和可预约状态
        for (TimeSlotDTO timeSlot : timeSlots) {
            if ("NORMAL".equals(role)) {
                // 普通用户 - 使用体验飞行容量
                timeSlot.setAvailableCapacity(
                    timeSlot.getExperienceCapacity() - timeSlot.getExperienceBookedCount()
                );
                timeSlot.setIsAvailable(timeSlot.getAvailableCapacity() > 0);
            } else if ("MEMBER".equals(role)) {
                // 学员 - 使用训练飞行容量
                timeSlot.setAvailableCapacity(
                    timeSlot.getTrainingCapacity() - timeSlot.getTrainingBookedCount()
                );
                timeSlot.setIsAvailable(timeSlot.getAvailableCapacity() > 0);
            } else {
                // 角色未知，默认不可用
                timeSlot.setAvailableCapacity(0);
                timeSlot.setIsAvailable(false);
            }
        }
    }
}