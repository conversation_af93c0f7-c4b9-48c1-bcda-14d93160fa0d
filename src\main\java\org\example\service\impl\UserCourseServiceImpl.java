package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.constants.Constants;
import org.example.dto.UserCourseDTO;
import org.example.entity.User;
import org.example.entity.UserCourse;
import org.example.mapper.UserCourseMapper;
import org.example.service.UserCourseService;
import org.example.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户课程服务实现类
 */
@Service
public class UserCourseServiceImpl implements UserCourseService {

    @Autowired
    private UserCourseMapper userCourseMapper;
    
    @Autowired
    private UserService userService;
    
    @Override
    @Transactional
    public boolean addUserCourse(UserCourse userCourse) {
        // 确保所有课时字段都有值，默认为0.0
        if (userCourse.getCompletedHours() == null) {
            userCourse.setCompletedHours(0.0);
        }
        if (userCourse.getTotalHours() == null) {
            userCourse.setTotalHours(0.0);
        }
        if (userCourse.getRemainingHours() == null) {
            userCourse.setRemainingHours(0.0);
        }
        
        userCourse.setCreateTime(LocalDateTime.now());
        userCourse.setUpdateTime(LocalDateTime.now());
        return userCourseMapper.insert(userCourse) > 0;
    }
    
    @Override
    @Transactional
    public boolean updateUserCourse(UserCourse userCourse) {
        userCourse.setUpdateTime(LocalDateTime.now());
        return userCourseMapper.updateById(userCourse) > 0;
    }
    
    @Override
    public UserCourse getUserCourse(Long id) {
        return userCourseMapper.selectById(id);
    }
    
    @Override
    public List<UserCourse> getUserCourses(Long userId) {
        LambdaQueryWrapper<UserCourse> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserCourse::getUserId, userId);
        return userCourseMapper.selectList(wrapper);
    }
    
    @Override
    @Transactional
    public boolean addCompletedHours(Long id, Double hours) {
        UserCourse userCourse = getUserCourse(id);
        if (userCourse == null) {
            return false;
        }
        
        // 计算新的已完成课时和剩余课时
        double newCompleted = userCourse.getCompletedHours() + hours;
        double newRemaining = userCourse.getRemainingHours() - hours;
        
        // 更新课时
        UserCourse updateCourse = new UserCourse();
        updateCourse.setId(id);
        updateCourse.setCompletedHours(newCompleted);
        updateCourse.setRemainingHours(newRemaining < 0 ? 0.0 : newRemaining);
        updateCourse.setUpdateTime(LocalDateTime.now());
        
        return userCourseMapper.updateById(updateCourse) > 0;
    }
    
    @Override
    @Transactional
    public boolean addRemainingHours(Long id, Double hours) {
        UserCourse userCourse = getUserCourse(id);
        if (userCourse == null) {
            return false;
        }
        
        // 更新剩余课时和总课时
        UserCourse updateCourse = new UserCourse();
        updateCourse.setId(id);
        updateCourse.setRemainingHours(userCourse.getRemainingHours() + hours);
        updateCourse.setTotalHours(userCourse.getTotalHours() + hours); // 增加剩余课时时，总课时也应增加
        updateCourse.setUpdateTime(LocalDateTime.now());
        
        return userCourseMapper.updateById(updateCourse) > 0;
    }
    
    @Override
    public Page<UserCourseDTO> pageUserCourseInfo(int current, int size, String username, String studentType) {
        // 创建分页对象
        Page<UserCourseDTO> resultPage = new Page<>(current, size);
        
        // 查询用户课程记录
        Page<UserCourse> userCoursePage = new Page<>(current, size);
        List<UserCourseDTO> resultList = new ArrayList<>();
        
        // 这里需要关联User表查询，可以使用自定义SQL或者在应用层处理
        // 这里采用应用层处理方式
        LambdaQueryWrapper<UserCourse> wrapper = new LambdaQueryWrapper<>();
        Page<UserCourse> coursePage = userCourseMapper.selectPage(userCoursePage, wrapper);
        
        // 转换为DTO
        for (UserCourse course : coursePage.getRecords()) {
            User user = userService.getUserById(course.getUserId());
            if (user != null) {
                // 如果有用户名过滤条件且不匹配，则跳过
                if (username != null && !username.isEmpty() && !user.getUsername().contains(username)) {
                    continue;
                }
                
                // 如果有学员类型过滤条件且不匹配，则跳过
                if (studentType != null && !studentType.isEmpty() && !studentType.equals(user.getStudentType())) {
                    continue;
                }
                
                UserCourseDTO dto = new UserCourseDTO();
                BeanUtils.copyProperties(course, dto);
                dto.setUsername(user.getUsername());
                dto.setStudentType(user.getStudentType());
                dto.setStudentTypeDesc(getStudentTypeDesc(user.getStudentType()));
                resultList.add(dto);
            }
        }
        
        // 设置结果
        resultPage.setRecords(resultList);
        resultPage.setTotal(resultList.size()); // 这里的总数不准确，实际应该使用count查询
        resultPage.setCurrent(current);
        resultPage.setSize(size);
        
        return resultPage;
    }
    
    @Override
    public UserCourseDTO getUserCourseInfo(Long id) {
        UserCourse userCourse = getUserCourse(id);
        if (userCourse == null) {
            return null;
        }
        
        User user = userService.getUserById(userCourse.getUserId());
        if (user == null) {
            return null;
        }
        
        UserCourseDTO dto = new UserCourseDTO();
        BeanUtils.copyProperties(userCourse, dto);
        dto.setUsername(user.getUsername());
        dto.setStudentType(user.getStudentType());
        dto.setStudentTypeDesc(getStudentTypeDesc(user.getStudentType()));
        
        return dto;
    }
    
    @Override
    @Transactional
    public boolean initUserCourse(Long userId, String studentType) {
        // 检查用户是否存在
        User user = userService.getUserById(userId);
        if (user == null) {
            return false;
        }
        
        // 设置用户的学员类型
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setStudentType(studentType);
        // 更新用户信息
        userService.updateUserInfo(updateUser);
        
        // 创建用户课时记录，所有课时初始为0.0
        UserCourse userCourse = new UserCourse();
        userCourse.setUserId(userId);
        userCourse.setTotalHours(0.0);
        userCourse.setCompletedHours(0.0);
        userCourse.setRemainingHours(0.0);
        userCourse.setCreateTime(LocalDateTime.now());
        userCourse.setUpdateTime(LocalDateTime.now());
        
        return userCourseMapper.insert(userCourse) > 0;
    }
	
	/**
	 * 根据用户ID查询用户课时信息
	 */
	@Override
	public UserCourse getUserCourseByUserId(Long userId) {
		if (userId == null) {
			return null;
		}
		
		// 创建查询条件
		LambdaQueryWrapper<UserCourse> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(UserCourse::getUserId, userId)
				.orderByDesc(UserCourse::getUpdateTime)
				.last("LIMIT 1");
		
		return userCourseMapper.selectOne(wrapper);
	}
	
	/**
     * 获取学员类型描述
     */
    private String getStudentTypeDesc(String studentType) {
        if (studentType == null) {
            return "未知";
        }
        
        switch (studentType) {
            case Constants.STUDENT_TYPE_SPORT_LICENSE:
                return "运动类执照学员";
            case Constants.STUDENT_TYPE_PRIVATE_LICENSE:
                return "私用驾驶员执照学员";
            case Constants.STUDENT_TYPE_SPORT_INSTRUCTOR:
                return "运动类教员学员";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取默认课时
     */
    private Double getDefaultHours(String studentType) {
        if (studentType == null) {
            return 0.0;
        }
        
        switch (studentType) {
            case Constants.STUDENT_TYPE_SPORT_LICENSE:
                return Constants.DEFAULT_HOURS_SPORT_LICENSE;
            case Constants.STUDENT_TYPE_PRIVATE_LICENSE:
                return Constants.DEFAULT_HOURS_PRIVATE_LICENSE;
            case Constants.STUDENT_TYPE_SPORT_INSTRUCTOR:
                return Constants.DEFAULT_HOURS_SPORT_INSTRUCTOR;
            default:
                return 0.0;
        }
    }
	
	
} 