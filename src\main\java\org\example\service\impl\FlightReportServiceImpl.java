package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.constants.Constants;
import org.example.entity.FlightReport;
import org.example.entity.User;
import org.example.entity.UserCourse;
import org.example.mapper.FlightReportMapper;
import org.example.service.FlightReportService;
import org.example.service.UserCourseService;
import org.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Duration;
import java.util.List;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 飞行报告服务实现类
 */
@Service
public class FlightReportServiceImpl implements FlightReportService {

    @Autowired
    private FlightReportMapper flightReportMapper;
    
    @Autowired
    private UserCourseService userCourseService;
    
    @Autowired
    private UserService userService;
    
    @Override
    @Transactional
    public boolean createFlightReport(FlightReport flightReport) {
        // 如果没有提供userCourseId，则尝试根据主驾驶或副驾驶姓名查找
        if (flightReport.getUserCourseId() == null) {
            // 至少需要提供主驾驶或副驾驶姓名
            if (flightReport.getPilotName() == null && flightReport.getCopilotName() == null) {
                return false;
            }
            
            // 用于记录找到的学员但没有课时记录的情况
            User foundUserWithoutCourse = null;
            
            // 尝试查找主驾驶对应的学员
            if (flightReport.getPilotName() != null) {
                User pilot = userService.getUserByUsername(flightReport.getPilotName());
                if (pilot != null) {
                    // 查找该学员的课时记录
                    List<UserCourse> courses = userCourseService.getUserCourses(pilot.getId());
                    if (!courses.isEmpty()) {
                        // 使用找到的第一条课时记录
                        flightReport.setUserCourseId(courses.get(0).getId());
                    } else {
                        // 记录找到了学员但没有课时记录
                        foundUserWithoutCourse = pilot;
                    }
                }
            }
            
            // 如果还没找到，尝试查找副驾驶对应的学员
            if (flightReport.getUserCourseId() == null && flightReport.getCopilotName() != null) {
                User copilot = userService.getUserByUsername(flightReport.getCopilotName());
                if (copilot != null) {
                    // 查找该学员的课时记录
                    List<UserCourse> courses = userCourseService.getUserCourses(copilot.getId());
                    if (!courses.isEmpty()) {
                        // 使用找到的第一条课时记录
                        flightReport.setUserCourseId(courses.get(0).getId());
                    } else if (foundUserWithoutCourse == null) {
                        // 记录找到了学员但没有课时记录
                        foundUserWithoutCourse = copilot;
                    }
                }
            }
            
            // 如果找到了学员但没有课时记录，抛出特定异常
            if (flightReport.getUserCourseId() == null && foundUserWithoutCourse != null) {
                throw new RuntimeException("学员 " + foundUserWithoutCourse.getUsername() + " 没有课时记录，请先添加课时信息");
            }
            
            // 如果仍然没找到课时记录，则失败
            if (flightReport.getUserCourseId() == null) {
                return false;
            }
        }
        
        // 获取user_course对象
        UserCourse userCourse = userCourseService.getUserCourse(flightReport.getUserCourseId());
        if (userCourse == null) {
            return false;
        }
        
        // 获取对应的学员信息
        User student = userService.getUserById(userCourse.getUserId());
        if (student == null) {
            return false;
        }
        
        // 验证学员姓名是否与主驾驶或副驾驶姓名匹配
        String studentName = student.getUsername();
        if (!studentName.equals(flightReport.getPilotName()) && 
            !studentName.equals(flightReport.getCopilotName())) {
            // 学员姓名必须是主驾驶或副驾驶之一
            return false;
        }
        
        // 如果没有设置飞行日期，则使用当前日期
        if (flightReport.getFlightDate() == null) {
            flightReport.setFlightDate(LocalDate.now());
        }
        
        // 设置创建者ID，这里使用默认值1表示系统管理员
        // 实际生产环境中应该从当前登录用户上下文获取
        if (flightReport.getCreatedBy() == null) {
            flightReport.setCreatedBy(1L);
        }

        // 设置默认报告状态为SUBMITTED（已提交）
        if (flightReport.getReportStatus() == null || flightReport.getReportStatus().trim().isEmpty()) {
            flightReport.setReportStatus(Constants.REPORT_STATUS_SUBMITTED);
        }
        
        // 验证必要时间字段
        if (flightReport.getStartupTime() == null || flightReport.getTakeoffTime() == null || 
            flightReport.getLandingTime() == null || flightReport.getShutdownTime() == null) {
            throw new RuntimeException("开车时间、起飞时间、着陆时间和关车时间均不能为空");
        }
        
        // 验证时间顺序
        if (flightReport.getStartupTime().isAfter(flightReport.getTakeoffTime()) ||
            flightReport.getTakeoffTime().isAfter(flightReport.getLandingTime()) ||
            flightReport.getLandingTime().isAfter(flightReport.getShutdownTime())) {
            throw new RuntimeException("时间顺序错误：开车时间 ≤ 起飞时间 ≤ 着陆时间 ≤ 关车时间");
        }
        
        // 计算空中时间（分钟）- 只考虑时分，忽略秒
        LocalTime takeoffTimeHM = LocalTime.of(flightReport.getTakeoffTime().getHour(), flightReport.getTakeoffTime().getMinute());
        LocalTime landingTimeHM = LocalTime.of(flightReport.getLandingTime().getHour(), flightReport.getLandingTime().getMinute());
        
        long airTimeInMinutes = Duration.between(
            takeoffTimeHM, 
            landingTimeHM
        ).toMinutes();
        
        // 转换为小时（小数点后2位）
        BigDecimal airTime = new BigDecimal(airTimeInMinutes / 60.0)
                           .setScale(2, RoundingMode.HALF_UP);
        flightReport.setAirTime(airTime);
        
        // 计算地面时间（分钟）- 只考虑时分，忽略秒
        // 开车到起飞的地面时间
        LocalTime startupTimeHM = LocalTime.of(flightReport.getStartupTime().getHour(), flightReport.getStartupTime().getMinute());
        
        long groundTime1InMinutes = Duration.between(
            startupTimeHM, 
            takeoffTimeHM
        ).toMinutes();
        
        // 着陆到关车的地面时间
        LocalTime shutdownTimeHM = LocalTime.of(flightReport.getShutdownTime().getHour(), flightReport.getShutdownTime().getMinute());
        
        long groundTime2InMinutes = Duration.between(
            landingTimeHM, 
            shutdownTimeHM
        ).toMinutes();
        
        // 总地面时间（小时，小数点后2位）
        long totalGroundTimeInMinutes = groundTime1InMinutes + groundTime2InMinutes;
        BigDecimal groundTime = new BigDecimal(totalGroundTimeInMinutes / 60.0)
                              .setScale(2, RoundingMode.HALF_UP);
        flightReport.setGroundTime(groundTime);
        
        // 计算合计时间（空中时间 + 地面时间）
        BigDecimal totalTime = airTime.add(groundTime).setScale(2, RoundingMode.HALF_UP);
        flightReport.setTotalHours(totalTime);
        
        // 验证总时间是否在合理范围内
        if (totalTime.compareTo(new BigDecimal("999.99")) > 0) {
            throw new RuntimeException("飞行时间过长，超出系统限制（最大999.99小时）");
        }
        
        // 更新学员课时信息 - 使用总时间更新学员课时（改为使用总时间而非仅空中时间）
        Double flightHours = totalTime.doubleValue();
        userCourseService.addCompletedHours(userCourse.getId(), flightHours);
        
        flightReport.setCreateTime(LocalDateTime.now());
        flightReport.setUpdateTime(LocalDateTime.now());
        return flightReportMapper.insert(flightReport) > 0;
    }
    
    @Override
    @Transactional
    public boolean updateFlightReport(FlightReport flightReport) {
        if (flightReport.getId() == null) {
            return false;
        }
        
        // 获取原始飞行报告
        FlightReport originalReport = flightReportMapper.selectById(flightReport.getId());
        if (originalReport == null) {
            return false;
        }
        
        // 如果更新了时间字段，重新计算飞行时间
        if (flightReport.getStartupTime() != null && flightReport.getTakeoffTime() != null &&
            flightReport.getLandingTime() != null && flightReport.getShutdownTime() != null) {
            
            // 验证时间顺序
            if (flightReport.getStartupTime().isAfter(flightReport.getTakeoffTime()) ||
                flightReport.getTakeoffTime().isAfter(flightReport.getLandingTime()) ||
                flightReport.getLandingTime().isAfter(flightReport.getShutdownTime())) {
                throw new RuntimeException("时间顺序错误：开车时间 ≤ 起飞时间 ≤ 着陆时间 ≤ 关车时间");
            }
            
            // 计算空中时间（分钟）- 只考虑时分，忽略秒
            LocalTime takeoffTimeHM = LocalTime.of(flightReport.getTakeoffTime().getHour(), flightReport.getTakeoffTime().getMinute());
            LocalTime landingTimeHM = LocalTime.of(flightReport.getLandingTime().getHour(), flightReport.getLandingTime().getMinute());
            
            long airTimeInMinutes = Duration.between(
                takeoffTimeHM, 
                landingTimeHM
            ).toMinutes();
            
            // 转换为小时（小数点后2位）
            BigDecimal airTime = new BigDecimal(airTimeInMinutes / 60.0)
                               .setScale(2, RoundingMode.HALF_UP);
            flightReport.setAirTime(airTime);
            
            // 计算地面时间 - 只考虑时分，忽略秒
            LocalTime startupTimeHM = LocalTime.of(flightReport.getStartupTime().getHour(), flightReport.getStartupTime().getMinute());
            LocalTime shutdownTimeHM = LocalTime.of(flightReport.getShutdownTime().getHour(), flightReport.getShutdownTime().getMinute());
            
            long groundTime1InMinutes = Duration.between(
                startupTimeHM, 
                takeoffTimeHM
            ).toMinutes();
            
            long groundTime2InMinutes = Duration.between(
                landingTimeHM, 
                shutdownTimeHM
            ).toMinutes();
            
            long totalGroundTimeInMinutes = groundTime1InMinutes + groundTime2InMinutes;
            BigDecimal groundTime = new BigDecimal(totalGroundTimeInMinutes / 60.0)
                                  .setScale(2, RoundingMode.HALF_UP);
            flightReport.setGroundTime(groundTime);
            
            // 计算合计时间
            BigDecimal totalTime = airTime.add(groundTime).setScale(2, RoundingMode.HALF_UP);
            flightReport.setTotalHours(totalTime);
            
            // 验证总时间是否在合理范围内
            if (totalTime.compareTo(new BigDecimal("999.99")) > 0) {
                throw new RuntimeException("飞行时间过长，超出系统限制（最大999.99小时）");
        }
        
            // 如果空中时间发生变化，更新学员课时信息
            if (originalReport.getUserCourseId() != null && 
                !originalReport.getTotalHours().equals(totalTime)) {
                
                // 计算总时间差值（改为使用总时间而非仅空中时间）
                double hoursDiff = totalTime.doubleValue() - originalReport.getTotalHours().doubleValue();
                
                // 更新学员课时
                if (hoursDiff != 0) {
                    userCourseService.addCompletedHours(originalReport.getUserCourseId(), hoursDiff);
                }
            }
        }
        
        flightReport.setUpdateTime(LocalDateTime.now());
        return flightReportMapper.updateById(flightReport) > 0;
    }
    
    @Override
    public FlightReport getFlightReport(Long id) {
        return flightReportMapper.selectById(id);
    }
	
    
    @Override
    public Page<FlightReport> pageFlightReports(int current, int size, String status, Long userCourseId, 
                                              LocalDate startDate, LocalDate endDate) {
        Page<FlightReport> page = new Page<>(current, size);
        LambdaQueryWrapper<FlightReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(status != null, FlightReport::getReportStatus, status)
               .eq(userCourseId != null, FlightReport::getUserCourseId, userCourseId);
        
        // 添加日期过滤条件
        if (startDate != null) {
            wrapper.ge(FlightReport::getFlightDate, startDate);
        }
        if (endDate != null) {
            wrapper.le(FlightReport::getFlightDate, endDate);
        }
        
        wrapper.orderByDesc(FlightReport::getFlightDate)
               .orderByDesc(FlightReport::getCreateTime);
               
        return flightReportMapper.selectPage(page, wrapper);
    }
	
    
    @Override
    public List<FlightReport> getUserFlightReports(Long userCourseId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<FlightReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlightReport::getUserCourseId, userCourseId);
        
        // 添加日期过滤条件
        if (startDate != null) {
            wrapper.ge(FlightReport::getFlightDate, startDate);
        }
        if (endDate != null) {
            wrapper.le(FlightReport::getFlightDate, endDate);
        }
        
        wrapper.orderByDesc(FlightReport::getFlightDate)
               .orderByDesc(FlightReport::getCreateTime);
                
        return flightReportMapper.selectList(wrapper);
    }
    
    @Override
    @Transactional
    public boolean deleteReport(Long id) {
        // 直接删除报告，不再检查状态
        return flightReportMapper.deleteById(id) > 0;
    }
    
    @Override
    public Page<FlightReport> pageFlightReportsByStudentName(int current, int size, String studentName,
                                                           String status, LocalDate startDate, LocalDate endDate) {
        Page<FlightReport> page = new Page<>(current, size);
        LambdaQueryWrapper<FlightReport> wrapper = new LambdaQueryWrapper<>();

        // 如果学员姓名不为空，则查询主驾驶或副驾驶中包含该姓名的报告
        if (studentName != null && !studentName.trim().isEmpty()) {
            wrapper.and(w -> w.like(FlightReport::getPilotName, studentName)
                          .or()
                          .like(FlightReport::getCopilotName, studentName));
        }

        // 添加状态筛选条件
        if (status != null && !status.trim().isEmpty()) {
            wrapper.eq(FlightReport::getReportStatus, status);
        }

        // 添加日期过滤条件
        if (startDate != null) {
            wrapper.ge(FlightReport::getFlightDate, startDate);
        }
        if (endDate != null) {
            wrapper.le(FlightReport::getFlightDate, endDate);
        }

        wrapper.orderByDesc(FlightReport::getFlightDate)
               .orderByDesc(FlightReport::getCreateTime);

        return flightReportMapper.selectPage(page, wrapper);
    }
    
    @Override
    public List<FlightReport> getFlightReportsByAircraftAndDate(String aircraftRegNumber, LocalDate date) {
        LambdaQueryWrapper<FlightReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlightReport::getAircraftRegNumber, aircraftRegNumber)
               .eq(FlightReport::getFlightDate, date)
               .orderByAsc(FlightReport::getStartupTime);
        
        return flightReportMapper.selectList(wrapper);
    }
} 