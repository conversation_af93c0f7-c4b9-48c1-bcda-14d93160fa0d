package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.dto.AircraftDTO;
import org.example.dto.AppointmentDTO;
import org.example.dto.FlightExperienceDTO;
import org.example.dto.TimeSlotDTO;
import org.example.entity.Appointment;
import org.example.entity.TimeSlot;
import org.example.entity.User;
import org.example.mapper.AppointmentMapper;
import org.example.service.AircraftService;
import org.example.service.AppointmentService;
import org.example.service.FlightExperienceService;

import org.example.service.TimeSlotService;
import org.example.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预约服务实现类
 */
@Service
public class AppointmentServiceImpl implements AppointmentService {

    @Autowired
    private AppointmentMapper appointmentMapper;
    
    @Autowired
    private TimeSlotService timeSlotService;
    
    @Autowired
    private UserService userService;
    
    @Override
    @Transactional
    public boolean createAppointment(Appointment appointment) {
        // 设置默认状态和创建时间
        appointment.setStatus("PENDING");
        appointment.setCreateTime(LocalDateTime.now());
        appointment.setUpdateTime(LocalDateTime.now());
        
        // 获取用户信息
        User user = userService.getUserById(appointment.getUserId());
        if (user == null) {
            return false;
        }
        
        // 判断用户类型，学员(MEMBER)预约训练时间段，其他用户预约体验时间段
        boolean isExperience = !"MEMBER".equals(user.getRole());
        
        // 检查该时间段在该日期是否有可用容量
        if (!isTimeSlotAvailable(appointment.getTimeSlotId(), isExperience)) {
            return false; // 时间段已满或不可用
        }
        
        // 同步时间段信息到预约记录中
        syncTimeSlotInfo(appointment);
        
        boolean success = appointmentMapper.insert(appointment) > 0;
        
        // 更新时间段预约计数
        if (success) {
            timeSlotService.incrementBookedCount(appointment.getTimeSlotId(), isExperience);
        }
        
        return success;
    }
	
	
	/**
     * 检查时间段是否可用于预约
     */
    private boolean isTimeSlotAvailable(Long timeSlotId, boolean isExperience) {
        if (timeSlotId == null) {
            return false;
        }
        
        // 获取时间段信息
        TimeSlot timeSlot = timeSlotService.getTimeSlot(timeSlotId);
        if (timeSlot == null) {
            return false;
        }
        
        // 检查时间段状态
        if (timeSlot.getStatus() != 1) { // 1表示可用
            return false;
        }
        
        // 检查是否有可用容量
        return timeSlotService.hasAvailableCapacity(timeSlotId, isExperience);
    }
    
    /**
     * 检查时间段是否可用于体验预约
     * @deprecated 使用 isTimeSlotAvailable 替代
     */
    @Deprecated
    private boolean isTimeSlotAvailableForExperience(Long timeSlotId) {
        return isTimeSlotAvailable(timeSlotId, true);
    }
    
    @Override
    public Appointment getAppointment(Long id) {
        return appointmentMapper.selectById(id);
    }

    @Override
    public AppointmentDTO getAppointmentDTO(Long id) {
        Appointment appointment = getAppointment(id);
        if (appointment == null) {
            return null;
        }
        
        return convertToDTO(appointment);
    }

    @Override
    @Transactional
    public boolean updateAppointmentStatus(Long id, String status) {
        Appointment appointment = getAppointment(id);
        if (appointment == null) {
            return false;
        }
        
        // 获取用户信息
        User user = userService.getUserById(appointment.getUserId());
        if (user == null) {
            return false;
        }
        
        // 判断用户类型，学员(STUDENT)预约训练时间段，其他用户预约体验时间段
        boolean isExperience = !"STUDENT".equals(user.getRole());
	
	    // 如果状态变为取消，释放时间段
	    if ("CANCELLED".equals(status)) {
		    if (!"CANCELLED".equals(appointment.getStatus()) && !"COMPLETED".equals(appointment.getStatus())) {
                timeSlotService.decrementBookedCount(appointment.getTimeSlotId(), isExperience);
            }
        }
        
        appointment.setStatus(status);
        appointment.setUpdateTime(LocalDateTime.now());
        return appointmentMapper.updateById(appointment) > 0;
    }

    @Override
    @Transactional
    public boolean updateAppointment(Long id, LocalDate appointmentDate, Long timeSlotId, 
                                    Long aircraftId, String remark) {
        // 先获取原预约信息
        Appointment original = getAppointment(id);
        if (original == null) {
            return false;
        }
        
        // 获取用户信息
        User user = userService.getUserById(original.getUserId());
        if (user == null) {
            return false;
        }
        
        // 判断用户类型，学员(STUDENT)预约训练时间段，其他用户预约体验时间段
        boolean isExperience = !"STUDENT".equals(user.getRole());
        
        // 如果修改了时间段，需要检查新时间段是否可用
        if (!original.getTimeSlotId().equals(timeSlotId)) {
            if (!isTimeSlotAvailable(timeSlotId, isExperience)) {
                return false; // 新时间段已满或不可用
            }
            
            // 释放原时间段
            timeSlotService.decrementBookedCount(original.getTimeSlotId(), isExperience);
            
            // 占用新时间段
            if (!timeSlotService.incrementBookedCount(timeSlotId, isExperience)) {
                // 如果新时间段预约失败，恢复原时间段
                timeSlotService.incrementBookedCount(original.getTimeSlotId(), isExperience);
                return false;
            }
        }
        
        // 更新预约信息
        Appointment appointment = new Appointment();
        appointment.setId(id);
        appointment.setAppointmentDate(appointmentDate);
        appointment.setTimeSlotId(timeSlotId);
        appointment.setRemark(remark);
        appointment.setUpdateTime(LocalDateTime.now());
        
        // 同步时间段信息
        syncTimeSlotInfo(appointment);
        
        return appointmentMapper.updateById(appointment) > 0;
    }

    @Override
    @Transactional
    public boolean cancelAppointment(Long id) {
        return updateAppointmentStatus(id, "CANCELLED");
    }
	
	@Override
    @Transactional
    public boolean confirmAppointment(Long id) {
        return updateAppointmentStatus(id, "CONFIRMED");
    }
    
    @Override
    @Transactional
    public boolean completeAppointment(Long id) {
        return updateAppointmentStatus(id, "COMPLETED");
    }
    
    @Override
    public List<Appointment> getUserAppointments(Long userId) {
        LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Appointment::getUserId, userId)
               .orderByDesc(Appointment::getCreateTime);
        return appointmentMapper.selectList(wrapper);
    }

    @Override
    public List<AppointmentDTO> getUserAppointmentDTOs(Long userId) {
        List<Appointment> appointments = getUserAppointments(userId);
        return appointments.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
	
	@Override
    public Page<Appointment> pageAppointments(int current, int size, String status, Long userId, LocalDate startDate, LocalDate endDate) {
        Page<Appointment> page = new Page<>(current, size);
        LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(status != null, Appointment::getStatus, status)
               .eq(userId != null, Appointment::getUserId, userId)
               .ge(startDate != null, Appointment::getAppointmentDate, startDate)
               .le(endDate != null, Appointment::getAppointmentDate, endDate)
               .orderByDesc(Appointment::getCreateTime);
        return appointmentMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<AppointmentDTO> pageAppointmentDTOs(int current, int size, String status, Long userId, LocalDate startDate, LocalDate endDate) {
        Page<Appointment> page = pageAppointments(current, size, status, userId, startDate, endDate);
        
        // 转换为DTO
        Page<AppointmentDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(page, dtoPage, "records");
        
        List<AppointmentDTO> dtoList = page.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    @Override
    public boolean hasUserAppointmentAtTimeSlot(Long userId, LocalDate date, Long timeSlotId) {
        LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Appointment::getUserId, userId)
               .eq(Appointment::getAppointmentDate, date)
               .eq(Appointment::getTimeSlotId, timeSlotId)
               .ne(Appointment::getStatus, "CANCELLED");
        return appointmentMapper.selectCount(wrapper) > 0;
    }
    
    @Override
    @Transactional
    public boolean syncTimeSlotInfo(Appointment appointment) {
        if (appointment == null || appointment.getTimeSlotId() == null) {
            return false;
        }
        
        // 获取时间段信息
        TimeSlot timeSlot = timeSlotService.getTimeSlot(appointment.getTimeSlotId());
        if (timeSlot == null) {
            return false;
        }
        
        // 将时间段信息同步到预约记录中
        appointment.setStartTime(timeSlot.getStartTime());
        appointment.setEndTime(timeSlot.getEndTime());
        
        return true;
    }
    
    @Override
    @Transactional
    public int syncAllAppointmentsTimeSlotInfo() {
        int count = 0;
        
        // 获取所有预约记录
        LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(Appointment::getTimeSlotId);
        List<Appointment> appointments = appointmentMapper.selectList(wrapper);
        
        // 同步每个预约记录的时间段信息
        for (Appointment appointment : appointments) {
            if (syncTimeSlotInfo(appointment)) {
                appointmentMapper.updateById(appointment);
                count++;
            }
        }
        
        return count;
    }
	
	@Override
	public int countTodayAppointments(LocalDate date) {
		LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Appointment::getAppointmentDate, date);
		return appointmentMapper.selectCount(wrapper).intValue();
	}
    
    @Override
    public List<Map<String, Object>> getAppointmentDistribution(LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> distribution = new ArrayList<>();
        
        // 查询指定日期范围内的预约
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(startDate != null, Appointment::getAppointmentDate, startDate)
                    .le(endDate != null, Appointment::getAppointmentDate, endDate);
        
        List<Appointment> appointments = appointmentMapper.selectList(queryWrapper);
        
        // 统计体验飞行数量（EXPERIENCE_ONLY）
        long experienceCount = appointments.stream()
                .filter(appointment -> "EXPERIENCE_ONLY".equals(appointment.getTimeSlotPurpose()))
                .count();
        
        // 统计飞行培训数量（TRAINING_ONLY）
        long trainingCount = appointments.stream()
                .filter(appointment -> "TRAINING_ONLY".equals(appointment.getTimeSlotPurpose()))
                .count();
        
        // 统计其他数量（ALL或NULL）
        long otherCount = appointments.stream()
                .filter(appointment -> appointment.getTimeSlotPurpose() == null || 
                       "ALL".equals(appointment.getTimeSlotPurpose()))
                .count();
        
        // 构建返回结果
        Map<String, Object> experienceMap = new HashMap<>();
        experienceMap.put("name", "体验飞行");
        experienceMap.put("value", experienceCount);
        experienceMap.put("itemStyle", Map.of("color", "#FF6B6B"));
        distribution.add(experienceMap);
        
        Map<String, Object> trainingMap = new HashMap<>();
        trainingMap.put("name", "飞行培训");
        trainingMap.put("value", trainingCount);
        trainingMap.put("itemStyle", Map.of("color", "#4FD8FF"));
        distribution.add(trainingMap);
        
        Map<String, Object> otherMap = new HashMap<>();
        otherMap.put("name", "其他");
        otherMap.put("value", otherCount);
        otherMap.put("itemStyle", Map.of("color", "#FFD166"));
        distribution.add(otherMap);
        
        return distribution;
    }
    
    /**
     * 将实体转换为DTO
     */
    private AppointmentDTO convertToDTO(Appointment appointment) {
        if (appointment == null) {
            return null;
        }
        
        AppointmentDTO dto = new AppointmentDTO();
        BeanUtils.copyProperties(appointment, dto);
		
        // 优先使用已同步的时间段信息，如果没有才从TimeSlot表获取
        if (appointment.getStartTime() != null && appointment.getEndTime() != null) {
            // 使用已同步的时间段信息
            TimeSlotDTO timeSlotDTO = new TimeSlotDTO();
            timeSlotDTO.setStartTime(appointment.getStartTime());
            timeSlotDTO.setEndTime(appointment.getEndTime());
            timeSlotDTO.setPurpose(appointment.getTimeSlotPurpose());
            dto.setTimeSlot(timeSlotDTO);
        } else if (appointment.getTimeSlotId() != null) {
            // 如果没有同步信息，从TimeSlot表获取
            dto.setTimeSlot(timeSlotService.getTimeSlotDTO(appointment.getTimeSlotId()));
        }
        
        // 获取并设置用户名信息
        if (appointment.getUserId() != null) {
            User user = userService.getUserById(appointment.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
            }
        }
        
        return dto;
    }
} 