package org.example.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.example.dto.AppointmentDTO;
import org.example.entity.Appointment;
import org.example.service.AppointmentService;
import org.example.service.SystemConfigService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 预约控制器
 * 支持所有类型用户（客户和学员）的预约功能
 */
@RestController
@RequestMapping("/appointment")
@Slf4j
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;
    
    @Autowired
    private SystemConfigService systemConfigService;
	

    /**
     * 获取预约详情
     *
     * @param id 预约ID
     * @return 预约详情
     */
    @GetMapping("/{id}")
    public Result<AppointmentDTO> getDetail(@PathVariable Long id) {
        AppointmentDTO detail = appointmentService.getAppointmentDTO(id);
        return detail != null ? Result.success(detail) : Result.error("预约不存在");
    }

    /**
     * 更新预约
     *
     * @param id              预约ID
     * @param appointmentDate 预约日期
     * @param timeSlotId      时间段ID
     * @param aircraftId      飞机ID
     * @param remark          备注
     * @return 是否成功
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateAppointment(
            @PathVariable Long id,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate appointmentDate,
            @RequestParam Long timeSlotId,
            @RequestParam Long aircraftId,
            @RequestParam(required = false) String remark) {

        log.info("更新预约：id={}, date={}, timeSlotId={}, aircraftId={}",
                id, appointmentDate, timeSlotId, aircraftId);

        boolean success = appointmentService.updateAppointment(id, appointmentDate, timeSlotId, 
                                                            aircraftId, remark);
        return success ? Result.success(true) : Result.error("更新失败");
    }

    /**
     * 取消预约
     *
     * @param id 预约ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> cancelAppointment(@PathVariable Long id) {
        log.info("取消预约：id={}", id);
        boolean success = appointmentService.cancelAppointment(id);
        return success ? Result.success(true) : Result.error("取消失败");
    }
    
    /**
     * 确认预约
     *
     * @param id 预约ID
     * @return 是否成功
     */
    @PutMapping("/confirm/{id}")
    public Result<Boolean> confirmAppointment(@PathVariable Long id) {
        log.info("确认预约：id={}", id);
        boolean success = appointmentService.confirmAppointment(id);
        return success ? Result.success(true) : Result.error("确认失败");
    }
    
    /**
     * 完成预约
     *
     * @param id 预约ID
     * @return 是否成功
     */
    @PutMapping("/complete/{id}")
    public Result<Boolean> completeAppointment(@PathVariable Long id) {
        log.info("完成预约：id={}", id);
        boolean success = appointmentService.completeAppointment(id);
        return success ? Result.success(true) : Result.error("完成失败");
    }

    /**
     * 获取用户所有预约
     *
     * @param userId 用户ID
     * @return 预约列表
     */
    @GetMapping("/user/{userId}")
    public Result<List<AppointmentDTO>> getUserAppointments(@PathVariable Long userId) {
        log.info("获取用户预约：userId={}", userId);
        List<AppointmentDTO> list = appointmentService.getUserAppointmentDTOs(userId);
        return Result.success(list);
    }

    /**
     * 分页查询预约
     *
     * @param page      页码
     * @param size      每页大小
     * @param status    状态，可选
     * @param startDate 开始日期，可选
     * @param endDate   结束日期，可选
     * @return 分页结果，包含用户名等完整信息的AppointmentDTO
     */
    @GetMapping("/page")
    public Result<Page<AppointmentDTO>> page(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
		
        // 移除了userId参数，服务层需要确保返回的AppointmentDTO中包含用户名信息
        Page<AppointmentDTO> result = appointmentService.pageAppointmentDTOs(page, size, status, null, startDate, endDate);
        return Result.success(result);
    }
    
    /**
     * 检查时间段是否可预约
     */
    @GetMapping("/check")
    public Result<Boolean> checkTimeSlotAvailable(
            @RequestParam Long userId,
            @RequestParam Long timeSlotId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        // 检查预约功能是否开启
        if (!systemConfigService.isAppointmentEnabled()) {
            return Result.error("预约功能暂时关闭，请稍后再试");
        }
        
        // 获取用户信息并检查时间段可用性的逻辑已经移到服务层
        // 这里只需传递参数调用服务
        boolean available = !appointmentService.hasUserAppointmentAtTimeSlot(userId, date, timeSlotId);
        return Result.success(available);
    }
	
} 