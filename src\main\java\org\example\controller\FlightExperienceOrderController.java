package org.example.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.dto.ExperienceOrderDTO;
import org.example.dto.FlightExperienceDTO;
import org.example.dto.PaymentResponseDTO;
import org.example.service.FlightExperienceService;
import org.example.service.OrdersService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 飞行体验订单控制器
 */
@RestController
@RequestMapping("/experience/order")
@Slf4j
public class FlightExperienceOrderController {

    @Autowired
    private OrdersService ordersService;
    
    @Autowired
    private FlightExperienceService flightExperienceService;

    /**
     * 创建飞行体验订单
     *
     * @param orderDTO 订单信息
     * @return 支付信息
     */
    @PostMapping("/create")
    public Result<PaymentResponseDTO> createOrder(@RequestBody ExperienceOrderDTO orderDTO) {
        log.info("创建飞行体验订单：{}", orderDTO);
        
        if (orderDTO.getUserId() == null) {
            return Result.error("用户ID不能为空");
        }
        
        if (orderDTO.getExperienceId() == null) {
            return Result.error("体验项目ID不能为空");
        }
        
        // 获取体验项目信息
        FlightExperienceDTO experience = flightExperienceService.getDetail(orderDTO.getExperienceId());
        if (experience == null) {
            return Result.error("体验项目不存在");
        }
        
        // 使用通用订单创建方法
        PaymentResponseDTO response = ordersService.createProductOrder(
                orderDTO.getUserId(),
                "EXPERIENCE",
                orderDTO.getExperienceId(),
                experience.getPrice(),
                experience.getName());
        
        if (response == null) {
            return Result.error("创建订单失败，请检查参数是否正确");
        }
        
        return Result.success(response);
    }
    
    /**
     * 查询订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态
     */
    @GetMapping("/status")
    public Result<String> getOrderStatus(@RequestParam String orderNo) {
        log.info("查询订单状态：orderNo={}", orderNo);
        String status = ordersService.getOrderStatus(orderNo);
        if (status == null) {
            return Result.error("订单不存在");
        }
        return Result.success(status);
    }
    
    /**
     * 查询用户的飞行体验订单列表
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 订单列表
     */
    @GetMapping("/user/{userId}")
    public Result<?> getUserOrders(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("查询用户的飞行体验订单列表：userId={}, page={}, size={}", userId, page, size);
        return Result.success(ordersService.getUserProductTypeOrders(userId, "EXPERIENCE", page, size));
    }
} 