package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.example.entity.SystemConfig;
import org.example.mapper.SystemConfigMapper;
import org.example.service.SystemConfigService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 系统配置Service实现类
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {

    private static final String KEY_APPOINTMENT_ENABLED = "appointment_enabled";

    @Override
    public String getConfigValue(String key) {
        LambdaQueryWrapper<SystemConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemConfig::getConfigKey, key);
        SystemConfig config = this.getOne(wrapper);
        return config != null ? config.getConfigValue() : null;
    }

    @Override
    public boolean setConfigValue(String key, String value, String description) {
        LambdaQueryWrapper<SystemConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemConfig::getConfigKey, key);
        SystemConfig config = this.getOne(wrapper);
        
        LocalDateTime now = LocalDateTime.now();
        
        if (config == null) {
            config = new SystemConfig();
            config.setConfigKey(key);
            config.setConfigValue(value);
            config.setDescription(description);
            config.setCreateTime(now);
            config.setUpdateTime(now);
            return this.save(config);
        } else {
            config.setConfigValue(value);
            if (description != null && !description.isEmpty()) {
                config.setDescription(description);
            }
            config.setUpdateTime(now);
            return this.updateById(config);
        }
    }

    @Override
    public boolean isAppointmentEnabled() {
        String value = getConfigValue(KEY_APPOINTMENT_ENABLED);
        return value == null || "true".equalsIgnoreCase(value);
    }

    @Override
    public boolean setAppointmentEnabled(boolean enabled) {
        return setConfigValue(
            KEY_APPOINTMENT_ENABLED, 
            String.valueOf(enabled), 
            "预约功能是否开启"
        );
    }
} 