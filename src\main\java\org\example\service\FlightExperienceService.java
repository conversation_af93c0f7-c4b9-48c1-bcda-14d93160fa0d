package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.example.dto.FlightExperienceDTO;
import org.example.entity.FlightExperience;

import java.util.List;

/**
 * 飞行体验服务接口
 */
public interface FlightExperienceService extends IService<FlightExperience> {

    /**
     * 分页获取飞行体验项目列表
     *
     * @param page     分页参数
     * @param name     项目名称，可选
     * @param status   状态，可选
     * @return 分页结果
     */
    Page<FlightExperienceDTO> page(Page<FlightExperience> page, String name, Integer status);

    /**
     * 根据ID获取飞行体验项目详情
     *
     * @param id 项目ID
     * @return 飞行体验项目详情
     */
    FlightExperienceDTO getDetail(Long id);

    /**
     * 获取所有上架的飞行体验项目
     *
     * @return 飞行体验项目列表
     */
    List<FlightExperienceDTO> listAllAvailable();
    
    /**
     * 添加飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 添加后的飞行体验项目
     */
    FlightExperienceDTO addExperience(FlightExperienceDTO flightExperienceDTO);
    
    /**
     * 更新飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 更新后的飞行体验项目
     */
    FlightExperienceDTO updateExperience(FlightExperienceDTO flightExperienceDTO);
    
    /**
     * 删除飞行体验项目
     *
     * @param id 项目ID
     * @return 是否删除成功
     */
    boolean deleteExperience(Long id);
    
    /**
     * 修改飞行体验项目状态
     *
     * @param id     项目ID
     * @param status 状态：0下架，1上架
     * @return 是否修改成功
     */
    boolean changeStatus(Long id, Integer status);
} 