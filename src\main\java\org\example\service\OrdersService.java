package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.dto.PaymentResponseDTO;
import org.example.entity.Orders;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 订单服务接口
 */
public interface OrdersService {
    
    /**
     * 创建订单
     */
    Orders createOrder(Orders order);
    
    /**
     * 创建通用商品订单
     * 
     * @param userId 用户ID
     * @param productType 商品类型: EXPERIENCE, COURSE, PACKAGE
     * @param productId 商品ID
     * @param amount 支付金额
     * @param productName 商品名称
     * @return 支付响应DTO
     */
    PaymentResponseDTO createProductOrder(Long userId, String productType, Long productId, BigDecimal amount, String productName);
    
    /**
     * 支付订单（带支付方式）
     * 
     * @param orderNo 订单号
     * @param paymentMethod 支付方式
     * @param transactionId 交易流水号
     * @return 是否支付成功
     */
    boolean payOrder(String orderNo, String paymentMethod, String transactionId);
    
    /**
     * 取消订单
     */
    boolean cancelOrder(String orderNo);
    
    /**
     * 退款订单
     */
    boolean refundOrder(String orderNo);
    
    /**
     * 获取订单详情
     */
    Orders getOrder(Long id);
    
    /**
     * 根据订单号获取订单
     */
    Orders getOrderByNo(String orderNo);
    
    /**
     * 获取订单状态
     * 
     * @param orderNo 订单号
     * @return 订单状态
     */
    String getOrderStatus(String orderNo);
    
    /**
     * 分页查询订单
     */
    Page<Orders> pageOrders(int current, int size, String status, Long userId, String productType);
    
    /**
     * 查询用户所有订单
     */
    List<Orders> getUserOrders(Long userId);
    
    /**
     * 分页查询用户特定类型的订单
     * 
     * @param userId 用户ID
     * @param productType 商品类型
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    Page<Orders> getUserProductTypeOrders(Long userId, String productType, int page, int size);
	
	/**
	 * 统计指定日期范围内的订单数量
	 *
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 订单数量
	 */
	int countMonthlyOrders(LocalDate startDate, LocalDate endDate);
	
	/**
	 * 计算指定日期范围内的订单总金额
	 *
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 订单总金额
	 */
	BigDecimal calculateMonthlyIncome(LocalDate startDate, LocalDate endDate);
} 