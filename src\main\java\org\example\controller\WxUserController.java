package org.example.controller;

import com.alibaba.fastjson.JSONObject;
import org.example.entity.User;
import org.example.service.MsmService;
import org.example.service.UserService;
import org.example.utils.JwtUtil;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 微信小程序用户控制器
 * @Auther: 苏紫陌
 * @Date: 2025/7/20 15:44
 */
@RestController
@RequestMapping("/wx/user")
public class WxUserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private MsmService msmService;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    /**
     * 微信小程序登录
     * @param code 微信登录code
     * @return 登录结果，包含token和用户信息
     */
    @GetMapping("/login")
    public Result login(@RequestParam String code) {
        try {
            // 调用微信API获取openid
            String openid = userService.getWxOpenid(code);
            if (openid == null) {
                return Result.error("获取微信openid失败");
            }
            
            // 根据openid查询用户
            User user = userService.getUserByOpenid(openid);
            
            // 用户不存在，自动注册
            if (user == null) {
                user = new User();
                user.setOpenid(openid);
                user.setNickname("微信用户"); // 设置默认用户名
                user.setAvatar("/images/default/avatar.png"); // 设置默认头像
                user.setRole("NORMAL"); // 默认普通用户角色
                user.setStatus(1);      // 默认状态为正常
                userService.registerWxUser(user);
            }
            
            // 生成token - 使用静态方法
            String token = JwtUtil.createToken(user.getId(), user.getUsername(), user.getRole());
            
            // 构建返回结果
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("token", token);
            resultMap.put("user", user);
            
            return Result.success(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public Result updateUserInfo(@RequestBody User user) {
        try {
            // 获取当前登录用户ID（从JWT中获取）
            Long userId = userService.getCurrentUserId();
            if (userId == null) {
                return Result.error("未登录");
            }
            
            // 设置ID，确保只能修改自己的信息
            user.setId(userId);
            
            // 不允许修改关键字段
            user.setOpenid(null);
            user.setRole(null);
            user.setStatus(null);
            
            boolean success = userService.updateUserInfo(user);
            if (success) {
                // 获取更新后的用户信息
                User updatedUser = userService.getUserById(userId);
                return Result.success(updatedUser);
            } else {
                return Result.error("更新用户信息失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     * @return 用户信息
     */
    @GetMapping("/info")
    public Result getUserInfo() {
        try {
            // 获取当前登录用户ID（从JWT中获取）
            Long userId = userService.getCurrentUserId();
            if (userId == null) {
                return Result.error("未登录");
            }
            
            User user = userService.getUserById(userId);
            if (user != null) {
                return Result.success(user);
            } else {
                return Result.error("获取用户信息失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 手机号验证码登录
     * @param phone 手机号
     * @param code 验证码
     * @return 登录结果，包含token和用户信息
     */
    @PostMapping("/phone/login")
    public Result phoneLogin(@RequestParam String phone, @RequestParam String code) {
        // 参数校验
        if (StringUtils.isEmpty(phone)) {
            return Result.error("手机号不能为空");
        }
        
        if (StringUtils.isEmpty(code)) {
            return Result.error("验证码不能为空");
        }
        
        try {
            // 验证码校验
            boolean isValid = msmService.verifyCode(phone, code);
            if (!isValid) {
                return Result.error("验证码错误或已过期");
            }
            
            // 根据手机号查询用户
            User user = userService.getUserByPhone(phone);
            
            // 用户不存在，自动注册
            if (user == null) {
                user = new User();
                user.setPhone(phone);
                user.setNickname("用户" + phone.substring(phone.length() - 4)); // 使用手机号后4位作为用户名
                user.setAvatar("/images/default/avatar.png"); // 设置默认头像
                user.setRole("NORMAL"); // 默认普通用户角色
                user.setStatus(1);      // 默认状态为正常
                userService.registerPhoneUser(user);
            }
            
            // 生成token
            String token = JwtUtil.createToken(user.getId(), user.getUsername(), user.getRole());
            
            // 构建返回结果
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("token", token);
            resultMap.put("user", user);
            
            return Result.success(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送短信验证码
     * @param phone 手机号
     * @return 发送结果
     */
    @PostMapping("/sms/send")
    public Result sendSmsCode(@RequestParam String phone) {
        // 验证手机号格式
        if (StringUtils.isEmpty(phone)) {
            return Result.error("手机号不能为空");
        }
        
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            return Result.error("手机号格式不正确");
        }
        
        try {
            // 从redis获取验证码，如果获取到，直接返回成功
            String redisKey = "sms:code:" + phone;
            String existCode = redisTemplate.opsForValue().get(redisKey);
            if (!StringUtils.isEmpty(existCode)) {
                return Result.success("验证码已发送");
            }
            
            // 生成6位随机数字验证码
            String code = generateRandomCode(6);
            
            // 调用短信服务发送验证码
            boolean isSend = msmService.send(phone, code);
            
            // 如果发送成功，将验证码存入Redis，设置2分钟有效期
            if (isSend) {
                redisTemplate.opsForValue().set(redisKey, code, 2, TimeUnit.MINUTES);
                return Result.success("验证码发送成功");
            } else {
                return Result.error("验证码发送失败，请稍后重试");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("验证码发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 微信一键登录并获取手机号
     * @param loginParam 登录参数，包含loginCode和phoneCode
     * @return 登录结果
     */
    @PostMapping("/wxPhoneLogin")
    public Result wxPhoneLogin(@RequestBody Map<String, String> loginParam) {
        String loginCode = loginParam.get("loginCode");
        String phoneCode = loginParam.get("phoneCode");
        
        if (StringUtils.isEmpty(loginCode) || StringUtils.isEmpty(phoneCode)) {
            return Result.error("参数不完整");
        }
        
        try {
            // 1. 使用loginCode获取openid和unionid
            String openid = userService.getWxOpenid(loginCode);
            if (openid == null) {
                return Result.error("获取微信openid失败");
            }
            
            // 2. 使用phoneCode获取用户手机号
            String phoneNumber = userService.getWxPhoneNumber(phoneCode);
            if (phoneNumber == null) {
                return Result.error("获取手机号失败");
            }
            
            // 3. 根据openid查询用户
            User userByOpenid = userService.getUserByOpenid(openid);
            
            // 4. 根据手机号查询用户
            User userByPhone = userService.getUserByPhone(phoneNumber);
            
            User user = null;
            
            // 5. 判断用户是否已注册
            if (userByOpenid != null && userByPhone != null) {
                // 两个账号都存在但ID不同，需要合并账号
                if (!userByOpenid.getId().equals(userByPhone.getId())) {
                    // 合并账号逻辑，以微信账号为主，可根据业务需求调整
                    user = userService.mergeUsers(userByOpenid, userByPhone);
                } else {
                    // 同一个账号，直接使用
                    user = userByOpenid;
                }
            } else if (userByOpenid != null) {
                // 只有微信账号存在，更新手机号
                user = userByOpenid;
                user.setPhone(phoneNumber);
                userService.updateUserInfo(user);
            } else if (userByPhone != null) {
                // 只有手机号账号存在，更新openid
                user = userByPhone;
                user.setOpenid(openid);
                userService.updateUserInfo(user);
            } else {
                // 两个账号都不存在，创建新账号
                user = new User();
                user.setOpenid(openid);
                user.setPhone(phoneNumber);
                user.setNickname("微信用户"); // 设置默认用户名
                user.setAvatar("/images/default/avatar.png"); // 设置默认头像
                user.setRole("NORMAL"); // 默认普通用户角色
                user.setStatus(1);      // 默认状态为正常
                userService.registerWxUser(user);
            }
            
            // 6. 生成token
            String token = JwtUtil.createToken(user.getId(), user.getUsername(), user.getRole());
            
            // 7. 构建返回结果
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("token", token);
            resultMap.put("user", user);
            
            return Result.success(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 生成指定长度的随机数字验证码
     * @param length 验证码长度
     * @return 随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        
        return sb.toString();
    }
}
