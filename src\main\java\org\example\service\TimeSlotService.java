package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.example.dto.BatchTimeSlotDTO;
import org.example.dto.TimeSlotDTO;
import org.example.entity.TimeSlot;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 时间段服务接口
 */
public interface TimeSlotService extends IService<TimeSlot> {
    
    /**
     * 添加时间段
     */
    boolean addTimeSlot(TimeSlotDTO timeSlotDTO);
    
    /**
     * 更新时间段
     */
    boolean updateTimeSlot(TimeSlotDTO timeSlotDTO);
    
    /**
     * 删除时间段
     */
    boolean removeTimeSlot(Long id);
    
    /**
     * 获取时间段详情
     */
    TimeSlot getTimeSlot(Long id);
    
    /**
     * 获取时间段DTO
     * 
     * @param id 时间段ID
     * @return 时间段DTO
     */
    TimeSlotDTO getTimeSlotDTO(Long id);
    
    /**
     * 查询所有可用时间段
     */
    List<TimeSlot> listAvailableTimeSlots();
    
    /**
     * 分页查询时间段
     */
    Page<TimeSlotDTO> pageTimeSlots(int current, int size, Integer status);
    
    /**
     * 检查时间段是否有冲突
     * @return 有冲突返回true
     */
    boolean checkTimeConflict(LocalTime startTime, LocalTime endTime, Long id);
    
    /**
     * 批量创建时间段
     * @param batchDTO 批量创建参数
     * @return 创建成功的数量
     */
    int batchCreateTimeSlots(BatchTimeSlotDTO batchDTO);
    
    /**
     * 根据日期查询时间段
     * @param date 日期
     * @param purpose 用途(可选)
     * @return 时间段列表
     */
    List<TimeSlotDTO> listTimeSlotsByDate(LocalDate date, String purpose);
    
    /**
     * 根据日期范围查询时间段
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param purpose 用途(可选)
     * @return 时间段列表
     */
    List<TimeSlotDTO> listTimeSlotsByDateRange(LocalDate startDate, LocalDate endDate, String purpose);
    
    /**
     * 更新时间段预约状态
     * @param id 时间段ID
     * @param isExperience 是否为体验预约
     * @param booked 是否已预约
     * @return 是否成功
     * @deprecated 已弃用，请使用incrementBookedCount和decrementBookedCount方法
     */
    @Deprecated
    boolean updateBookedStatus(Long id, boolean isExperience, boolean booked);
    
    /**
     * 批量更新时间段状态
     * @param ids 时间段ID列表
     * @param status 状态
     * @return 更新成功的数量
     */
    int batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 增加预约计数
     * @param id 时间段ID
     * @param isExperience 是否为体验预约
     * @return 是否成功，如果容量已满则返回false
     */
    boolean incrementBookedCount(Long id, boolean isExperience);
    
    /**
     * 减少预约计数
     * @param id 时间段ID
     * @param isExperience 是否为体验预约
     * @return 是否成功
     */
    boolean decrementBookedCount(Long id, boolean isExperience);
    
    /**
     * 检查时间段是否有可用容量
     * @param id 时间段ID
     * @param isExperience 是否为体验预约
     * @return 是否有可用容量
     */
    boolean hasAvailableCapacity(Long id, boolean isExperience);
    
    /**
     * 批量设置某一天的所有时间段状态
     * @param date 日期
     * @param status 状态(0-不可用，1-可用)
     * @param description 原因描述
     * @return 是否成功
     */
    boolean setDateSlotsStatus(LocalDate date, Integer status, String description);
    
    /**
     * 批量设置日期范围内的所有时间段状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param status 状态(0-不可用，1-可用)
     * @param description 原因描述
     * @return 是否成功
     */
    boolean setDateRangeSlotsStatus(LocalDate startDate, LocalDate endDate, Integer status, String description);
    
    /**
     * 为指定日期创建默认时间段（如果不存在）并设置状态
     * @param date 日期
     * @param status 状态(0-不可用，1-可用)
     * @param description 原因描述
     * @return 是否成功
     */
    boolean createAndSetDateSlots(LocalDate date, Integer status, String description);
    
    /**
     * 获取指定日期范围内有可用时间段的日期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param purpose 用途(可选)
     * @return 可用日期列表
     */
    List<LocalDate> getAvailableDays(LocalDate startDate, LocalDate endDate, String purpose);
} 