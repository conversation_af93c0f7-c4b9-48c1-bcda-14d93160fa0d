package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.example.entity.AdminUser;
import org.example.entity.Menu;
import org.example.mapper.MenuMapper;
import org.example.service.AdminUserService;
import org.example.service.MenuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {
	
    
    @Autowired
    private MenuMapper menuMapper;
    
    @Autowired
    private AdminUserService adminUserService;
    
    @Override
    public List<Menu> getAllMenus() {
        return menuMapper.getAllMenus();
    }
    
    @Override
    public List<Menu> getMenuTree() {
        List<Menu> allMenus = getAllMenus();
        return buildMenuTree(allMenus);
    }
    
    @Override
    public List<Menu> getMenusByRoleId(Long roleId) {
        return menuMapper.getMenusByRoleId(roleId);
    }
    
    @Override
    public List<Menu> getMenuTreeByRoleId(Long roleId) {
        List<Menu> menus = getMenusByRoleId(roleId);
        return buildMenuTree(menus);
    }
    
    @Override
    public List<Menu> getMenusByUserId(Long userId) {
        // 获取用户信息
        try {
            // 尝试获取管理员用户信息，如果不存在则使用模拟数据
            AdminUser adminUser = adminUserService.findById(userId);
            if (adminUser == null) {
                return getMockMenus();
            }
            
            // 从数据库获取真实菜单数据
            List<Menu> menus;
            
            // 根据角色获取菜单
            String role = "admin"; // 假设所有管理员都是admin角色
            if ("admin".equals(role)) {
                // 管理员角色，获取所有菜单
                menus = getAllMenus();
            } else {
                // 其他角色，根据角色ID获取菜单
                // 这里简化处理，实际应该根据用户的角色ID获取
                Long roleId = 1L; // 默认角色ID
                menus = getMenusByRoleId(roleId);
            }
            
            // 如果数据库中没有菜单，则使用模拟数据
            if (menus == null || menus.isEmpty()) {
                return getMockMenus();
            }
            
            return menus;
        } catch (Exception e) {
            return getMockMenus();
        }
    }
    
    @Override
    public List<Menu> getMenuTreeByUserId(Long userId) {
        List<Menu> menus = getMenusByUserId(userId);
        return buildMenuTree(menus);
    }
    
    @Override
    public boolean saveMenu(Menu menu) {
        return save(menu);
    }
    
    @Override
    public boolean updateMenu(Menu menu) {
        return updateById(menu);
    }
    
    @Override
    public boolean deleteMenu(Long id) {
        // 查询是否有子菜单
        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getParentId, id);
        long count = count(queryWrapper);
        if (count > 0) {
            return false;
        }
        
        return removeById(id);
    }
    
    /**
     * 构建菜单树
     */
    private List<Menu> buildMenuTree(List<Menu> menus) {
        
        // 确保每个菜单的parentId不为null
        for (Menu menu : menus) {
            if (menu.getParentId() == null) {
                menu.setParentId(0L);
            }
        }
        
        // 先找出所有的一级菜单
        List<Menu> rootMenus = menus.stream()
                .filter(menu -> menu.getParentId() == 0)
                .collect(Collectors.toList());
		
        // 为一级菜单设置子菜单
        rootMenus.forEach(menu -> {
            List<Menu> children = getChildMenus(menu.getId(), menus);
            menu.setChildren(children);
        });
        
        return rootMenus;
    }
    
    /**
     * 递归获取子菜单
     */
    private List<Menu> getChildMenus(Long parentId, List<Menu> allMenus) {
        List<Menu> childMenus = allMenus.stream()
                .filter(menu -> parentId.equals(menu.getParentId()))
                .collect(Collectors.toList());
        
        childMenus.forEach(menu -> {
            List<Menu> children = getChildMenus(menu.getId(), allMenus);
            menu.setChildren(children);
        });
        
        return childMenus;
    }
    
    /**
     * 获取模拟菜单数据
     */
    private List<Menu> getMockMenus() {
        List<Menu> menus = new ArrayList<>();
        
        // 仪表盘
        Menu dashboard = new Menu();
        dashboard.setId(1L);
        dashboard.setParentId(0L);
        dashboard.setName("仪表盘");
        dashboard.setUrl("home");
        dashboard.setType(1);
        dashboard.setIcon("Odometer");
        dashboard.setSort(1);
        dashboard.setIframe(0);
        dashboard.setStatus(1);
        menus.add(dashboard);
        
        // 系统管理
        Menu system = new Menu();
        system.setId(5L);
        system.setParentId(0L);
        system.setName("系统管理");
        system.setUrl("system");
        system.setType(0);
        system.setIcon("Setting");
        system.setSort(2);
        system.setIframe(0);
        system.setStatus(1);
        menus.add(system);
        
        // 用户列表
        Menu userList = new Menu();
        userList.setId(3L);
        userList.setParentId(5L);
        userList.setName("用户列表");
        userList.setUrl("user/list");
        userList.setType(1);
        userList.setIcon("User");
        userList.setSort(1);
        userList.setIframe(0);
        userList.setStatus(1);
        menus.add(userList);
        
        // 菜单管理
        Menu menuManagement = new Menu();
        menuManagement.setId(6L);
        menuManagement.setParentId(5L);
        menuManagement.setName("菜单管理");
        menuManagement.setUrl("system/Menus");
        menuManagement.setType(1);
        menuManagement.setIcon("Menu");
        menuManagement.setSort(2);
        menuManagement.setIframe(0);
        menuManagement.setStatus(1);
        menus.add(menuManagement);
        
        // 角色管理
        Menu roleManagement = new Menu();
        roleManagement.setId(7L);
        roleManagement.setParentId(5L);
        roleManagement.setName("角色管理");
        roleManagement.setUrl("system/Roles");
        roleManagement.setType(1);
        roleManagement.setIcon("Lock");
        roleManagement.setSort(3);
        roleManagement.setIframe(0);
        roleManagement.setStatus(1);
        menus.add(roleManagement);
        
        // 飞行体验管理
        Menu flightExperience = new Menu();
        flightExperience.setId(8L);
        flightExperience.setParentId(0L);
        flightExperience.setName("飞行体验管理");
        flightExperience.setUrl("flight-experience");
        flightExperience.setType(0);
        flightExperience.setIcon("Airplane");
        flightExperience.setSort(3);
        flightExperience.setIframe(0);
        flightExperience.setStatus(1);
        menus.add(flightExperience);
        
        // 体验项目列表
        Menu experienceList = new Menu();
        experienceList.setId(9L);
        experienceList.setParentId(8L);
        experienceList.setName("体验项目列表");
        experienceList.setUrl("flight-experience/list");
        experienceList.setType(1);
        experienceList.setIcon("List");
        experienceList.setSort(1);
        experienceList.setIframe(0);
        experienceList.setStatus(1);
        menus.add(experienceList);
        
        // 添加体验项目
        Menu addExperience = new Menu();
        addExperience.setId(10L);
        addExperience.setParentId(8L);
        addExperience.setName("添加体验项目");
        addExperience.setUrl("flight-experience/add");
        addExperience.setType(1);
        addExperience.setIcon("Plus");
        addExperience.setSort(2);
        addExperience.setIframe(0);
        addExperience.setStatus(1);
        menus.add(addExperience);
        
        // 飞行报告管理
        Menu flightReport = new Menu();
        flightReport.setId(11L);
        flightReport.setParentId(0L);
        flightReport.setName("飞行报告管理");
        flightReport.setUrl("flight-report");
        flightReport.setType(0);
        flightReport.setIcon("Document");
        flightReport.setSort(4);
        flightReport.setIframe(0);
        flightReport.setStatus(1);
        menus.add(flightReport);
        
        // 报告列表
        Menu reportList = new Menu();
        reportList.setId(12L);
        reportList.setParentId(11L);
        reportList.setName("报告列表");
        reportList.setUrl("flight-report/list");
        reportList.setType(1);
        reportList.setIcon("List");
        reportList.setSort(1);
        reportList.setIframe(0);
        reportList.setStatus(1);
        menus.add(reportList);
        
        // 创建报告
        Menu createReport = new Menu();
        createReport.setId(13L);
        createReport.setParentId(11L);
        createReport.setName("创建报告");
        createReport.setUrl("flight-report/create");
        createReport.setType(1);
        createReport.setIcon("Edit");
        createReport.setSort(2);
        createReport.setIframe(0);
        createReport.setStatus(1);
        menus.add(createReport);
        
        // 报告统计
        Menu reportStatistics = new Menu();
        reportStatistics.setId(14L);
        reportStatistics.setParentId(11L);
        reportStatistics.setName("报告统计");
        reportStatistics.setUrl("flight-report/statistics");
        reportStatistics.setType(1);
        reportStatistics.setIcon("PieChart");
        reportStatistics.setSort(3);
        reportStatistics.setIframe(0);
        reportStatistics.setStatus(1);
        menus.add(reportStatistics);
        
        return menus;
    }
} 