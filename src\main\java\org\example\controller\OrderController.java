package org.example.controller;

import lombok.extern.slf4j.Slf4j;
import org.example.dto.GenericOrderDTO;
import org.example.dto.PaymentResponseDTO;
import org.example.service.OrdersService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 通用订单控制器
 */
@RestController
@RequestMapping("/order")
@Slf4j
public class OrderController {

    @Autowired
    private OrdersService ordersService;

    /**
     * 创建通用商品订单
     *
     * @param orderDTO 订单信息
     * @return 支付信息
     */
    @PostMapping("/create")
    public Result<PaymentResponseDTO> createOrder(@RequestBody GenericOrderDTO orderDTO) {
        log.info("创建订单：{}", orderDTO);
        
        // 参数验证
        if (orderDTO.getUserId() == null) {
            return Result.error("用户ID不能为空");
        }
        
        if (orderDTO.getProductId() == null) {
            return Result.error("商品ID不能为空");
        }
        
        if (orderDTO.getProductType() == null || orderDTO.getProductType().isEmpty()) {
            return Result.error("商品类型不能为空");
        }
        
        if (orderDTO.getAmount() == null || orderDTO.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return Result.error("商品金额必须大于0");
        }
        
        if (orderDTO.getProductName() == null || orderDTO.getProductName().isEmpty()) {
            return Result.error("商品名称不能为空");
        }
        
        PaymentResponseDTO response = ordersService.createProductOrder(
                orderDTO.getUserId(), 
                orderDTO.getProductType(),
                orderDTO.getProductId(),
                orderDTO.getAmount(),
                orderDTO.getProductName());
        
        if (response == null) {
            return Result.error("创建订单失败，请检查参数是否正确");
        }
        
        return Result.success(response);
    }
    
    /**
     * 查询订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态
     */
    @GetMapping("/status")
    public Result<String> getOrderStatus(@RequestParam String orderNo) {
        log.info("查询订单状态：orderNo={}", orderNo);
        String status = ordersService.getOrderStatus(orderNo);
        if (status == null) {
            return Result.error("订单不存在");
        }
        return Result.success(status);
    }
    
    /**
     * 查询用户的所有订单
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 订单列表
     */
    @GetMapping("/user/{userId}")
    public Result<?> getUserOrders(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("查询用户的所有订单：userId={}, page={}, size={}", userId, page, size);
        return Result.success(ordersService.pageOrders(page, size, null, userId, null));
    }
    
    /**
     * 查询用户特定类型的订单
     *
     * @param userId 用户ID
     * @param productType 商品类型
     * @param page   页码
     * @param size   每页大小
     * @return 订单列表
     */
    @GetMapping("/user/{userId}/type/{productType}")
    public Result<?> getUserProductTypeOrders(
            @PathVariable Long userId,
            @PathVariable String productType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("查询用户的特定类型订单：userId={}, productType={}, page={}, size={}", 
                userId, productType, page, size);
        return Result.success(ordersService.getUserProductTypeOrders(userId, productType, page, size));
    }
	
    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    @PostMapping("/cancel")
    public Result<Boolean> cancelOrder(@RequestParam String orderNo) {
        log.info("取消订单：orderNo={}", orderNo);
        boolean success = ordersService.cancelOrder(orderNo);
        if (!success) {
            return Result.error("取消订单失败，订单可能不存在或状态不允许取消");
        }
        return Result.success(true);
    }
    
    /**
     * 申请退款
     *
     * @param orderNo 订单号
     * @return 退款申请结果
     */
    @PostMapping("/refund")
    public Result<Boolean> refundOrder(@RequestParam String orderNo) {
        log.info("申请退款：orderNo={}", orderNo);
        boolean success = ordersService.refundOrder(orderNo);
        if (!success) {
            return Result.error("申请退款失败，订单可能不存在或状态不允许退款");
        }
        return Result.success(true);
    }
} 