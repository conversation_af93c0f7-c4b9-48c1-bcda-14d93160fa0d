package org.example.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 批量创建时间段DTO
 */
@Data
public class BatchTimeSlotDTO {
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
    
    /**
     * 重复规则:ALL-每天,WEEKDAY-工作日,WEEKEND-周末,CUSTOM-自定义
     */
    private String repeatRule;
    
    /**
     * 自定义重复的星期几(1-7表示周一到周日)
     */
    private List<Integer> customDays;
    
    /**
     * 时间段模板列表
     */
    private List<TimeSlotTemplateDTO> templates;
} 