package org.example.controller.admin;

import org.example.service.AdminUserService;
import org.example.service.AppointmentService;
import org.example.service.OrdersService;
import org.example.service.UserService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.example.dto.AppointmentDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 仪表盘统计数据控制器
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
	private UserService userService;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private OrdersService ordersService;

    /**
     * 获取所有仪表盘统计数据
     * @return 统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取总用户数
        int totalUsers = userService.countTotalUsers();
        stats.put("totalUsers", totalUsers);
        
        // 获取今日预约数
        LocalDate today = LocalDate.now();
        int todayAppointments = appointmentService.countTodayAppointments(today);
        stats.put("todayAppointments", todayAppointments);
        
        // 获取本月订单数
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        int monthlyOrders = ordersService.countMonthlyOrders(firstDayOfMonth, today);
        stats.put("monthlyOrders", monthlyOrders);
        
        // 获取本月收入
        BigDecimal monthlyIncome = ordersService.calculateMonthlyIncome(firstDayOfMonth, today);
        stats.put("monthlyIncome", monthlyIncome);
        
        return Result.success(stats);
    }

    /**
     * 获取总用户数
     * @return 总用户数
     */
    @GetMapping("/total-users")
    public Result<Integer> getTotalUsers() {
        int totalUsers = userService.countTotalUsers();
        return Result.success(totalUsers);
    }

    /**
     * 获取今日预约数
     * @return 今日预约数
     */
    @GetMapping("/today-appointments")
    public Result<Integer> getTodayAppointments() {
        LocalDate today = LocalDate.now();
        int todayAppointments = appointmentService.countTodayAppointments(today);
        return Result.success(todayAppointments);
    }

    /**
     * 获取本月订单数
     * @return 本月订单数
     */
    @GetMapping("/monthly-orders")
    public Result<Integer> getMonthlyOrders() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        int monthlyOrders = ordersService.countMonthlyOrders(firstDayOfMonth, today);
        return Result.success(monthlyOrders);
    }

    /**
     * 获取本月收入
     * @return 本月收入
     */
    @GetMapping("/monthly-income")
    public Result<BigDecimal> getMonthlyIncome() {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = today.withDayOfMonth(1);
        BigDecimal monthlyIncome = ordersService.calculateMonthlyIncome(firstDayOfMonth, today);
        return Result.success(monthlyIncome);
    }
    
    /**
     * 获取预约分布数据
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 不同类型预约的数量分布
     */
    @GetMapping("/appointment-distribution")
    public Result<List<Map<String, Object>>> getAppointmentDistribution(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        
        // 如果未提供日期，默认使用当前月份
        if (startDate == null) {
            startDate = LocalDate.now().withDayOfMonth(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        // 调用服务层方法获取预约分布数据
        List<Map<String, Object>> distribution = appointmentService.getAppointmentDistribution(startDate, endDate);
        
        return Result.success(distribution);
    }
    
    /**
     * 获取订单趋势数据
     * @param period 时间周期：week, month, year
     * @return 指定时间周期的订单趋势数据
     */
    @GetMapping("/order-trend")
    public Result<Map<String, Object>> getOrderTrend(@RequestParam(defaultValue = "month") String period) {
        Map<String, Object> result = new HashMap<>();
        List<String> xAxisData = new ArrayList<>();
        List<Integer> seriesData = new ArrayList<>();
        
        // 根据不同时间周期生成数据
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter;
        
        switch (period) {
            case "week":
                // 最近7天
                formatter = DateTimeFormatter.ofPattern("MM-dd");
                for (int i = 6; i >= 0; i--) {
                    LocalDate date = today.minusDays(i);
                    xAxisData.add(date.format(formatter));
                    // 这里应该从数据库中获取真实数据
                    seriesData.add(40 + (int)(Math.random() * 40));
                }
                break;
            case "year":
                // 最近12个月
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                for (int i = 11; i >= 0; i--) {
                    LocalDate date = today.minusMonths(i);
                    xAxisData.add(date.format(formatter));
                    // 这里应该从数据库中获取真实数据
                    seriesData.add(40 + (int)(Math.random() * 50));
                }
                break;
            default:
                // 默认为月视图，显示当月各天数据
                formatter = DateTimeFormatter.ofPattern("dd");
                int daysInMonth = today.lengthOfMonth();
                for (int i = 1; i <= daysInMonth; i++) {
                    LocalDate date = today.withDayOfMonth(i);
                    xAxisData.add(date.format(formatter));
                    // 这里应该从数据库中获取真实数据
                    seriesData.add(30 + (int)(Math.random() * 30));
                }
                break;
        }
        
        result.put("xAxisData", xAxisData);
        result.put("seriesData", seriesData);
        
        return Result.success(result);
    }

    /**
     * 获取今日预约列表
     * @return 今日预约列表
     */
    @GetMapping("/today-appointment-list")
    public Result<List<AppointmentDTO>> getTodayAppointmentList() {
        LocalDate today = LocalDate.now();
        // 使用AppointmentService中的分页方法，但只查询今天的数据，不分页
        Page<AppointmentDTO> page = appointmentService.pageAppointmentDTOs(1, 100, null, null, today, today);
        return Result.success(page.getRecords());
    }
} 