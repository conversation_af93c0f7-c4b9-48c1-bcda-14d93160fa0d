package org.example;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Description:
 * @Auther: 苏紫陌
 * @Date: ${DATE} ${TIME}
 */

@SpringBootApplication
@MapperScan("org.example.mapper")
@EnableScheduling
@Slf4j
public class Main {
	public static void main(String[] args) {
		SpringApplication.run(Main.class);
		log.info("启动成功");
	}
}