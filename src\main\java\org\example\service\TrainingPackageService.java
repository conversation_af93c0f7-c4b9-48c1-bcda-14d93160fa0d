package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.TrainingPackage;

import java.util.List;

/**
 * 培训套餐服务接口
 */
public interface TrainingPackageService {
    
    /**
     * 添加培训套餐
     */
    boolean addTrainingPackage(TrainingPackage trainingPackage);
    
    /**
     * 更新培训套餐
     */
    boolean updateTrainingPackage(TrainingPackage trainingPackage);
    
    /**
     * 删除培训套餐
     */
    boolean removeTrainingPackage(Long id);
    
    /**
     * 获取培训套餐详情
     */
    TrainingPackage getTrainingPackage(Long id);
    
    /**
     * 分页查询培训套餐
     */
    Page<TrainingPackage> pageTrainingPackages(int current, int size, Integer status);
    
    /**
     * 查询所有可用培训套餐
     */
    List<TrainingPackage> listAvailablePackages();
    
    /**
     * 更新培训套餐状态
     */
    boolean updatePackageStatus(Long id, Integer status);
} 