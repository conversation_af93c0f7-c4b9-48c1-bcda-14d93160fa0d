package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.example.entity.AdminUser;
import org.example.mapper.AdminUserMapper;
import org.example.service.AdminUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 管理员用户服务实现类
 */
@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired
    private AdminUserMapper adminUserMapper;
    
    @Override
    public AdminUser login(String username, String password) {
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getUsername, username);
        AdminUser adminUser = adminUserMapper.selectOne(wrapper);
		
        // 添加加密后密码的日志
        String encryptedPassword = encryptPassword(password);
		
        if (adminUser != null && adminUser.getPassword().equals(encryptPassword(password))) {
            return adminUser;
        }
        return null;
    }
    
    @Override
    @Transactional
    public boolean createAdmin(AdminUser adminUser) {
        // 密码加密
        adminUser.setPassword(encryptPassword(adminUser.getPassword()));
        return adminUserMapper.insert(adminUser) > 0;
    }
    
    @Override
    @Transactional
    public boolean updateAdmin(AdminUser adminUser) {
        // 如果密码不为空，则需要加密
        if (StringUtils.isNotBlank(adminUser.getPassword())) {
            adminUser.setPassword(encryptPassword(adminUser.getPassword()));
        } else {
            // 密码为空，表示不修改密码
            adminUser.setPassword(null);
        }
        return adminUserMapper.updateById(adminUser) > 0;
    }
    
    @Override
    public Page<AdminUser> pageAdmins(int current, int size, String username) {
        Page<AdminUser> page = new Page<>(current, size);
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(username != null, AdminUser::getUsername, username);
        return adminUserMapper.selectPage(page, wrapper);
    }
    
    @Override
    public AdminUser getAdminDetail(Long id) {
        return adminUserMapper.selectById(id);
    }
    
    @Override
    @Transactional
    public boolean removeAdmin(Long id) {
        return adminUserMapper.deleteById(id) > 0;
    }
    
    @Override
    public AdminUser findByUsername(String username) {
        LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AdminUser::getUsername, username);
        return adminUserMapper.selectOne(wrapper);
    }
    
    @Override
    public AdminUser findById(Long id) {
        return adminUserMapper.selectById(id);
    }
    
    /**
     * 密码加密 - 纯MD5加密，不使用盐值
     */
    private String encryptPassword(String password) {
        // 直接使用MD5加密，不加盐
        return DigestUtils.md5DigestAsHex(password.getBytes(StandardCharsets.UTF_8));
    }
} 