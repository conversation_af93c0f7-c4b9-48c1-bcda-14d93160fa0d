package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.example.dto.FlightExperienceDTO;
import org.example.entity.FlightExperience;
import org.example.mapper.FlightExperienceMapper;
import org.example.service.FlightExperienceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞行体验服务实现类
 */
@Service
@Slf4j
public class FlightExperienceServiceImpl extends ServiceImpl<FlightExperienceMapper, FlightExperience> implements FlightExperienceService {

    /**
     * 分页获取飞行体验项目列表
     *
     * @param page     分页参数
     * @param name     项目名称，可选
     * @param status   状态，可选
     * @return 分页结果
     */
    @Override
    public Page<FlightExperienceDTO> page(Page<FlightExperience> page, String name, Integer status) {
        LambdaQueryWrapper<FlightExperience> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(FlightExperience::getName, name);
        }
        
        if (status != null) {
            queryWrapper.eq(FlightExperience::getStatus, status);
        }
        
        // 默认按创建时间降序排序
//        queryWrapper.orderByDesc(FlightExperience::getCreateTime);
        
        // 执行查询
        Page<FlightExperience> resultPage = this.page(page, queryWrapper);
        
        // 转换为DTO
        Page<FlightExperienceDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(resultPage, dtoPage, "records");
        
        List<FlightExperienceDTO> dtoList = resultPage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    /**
     * 根据ID获取飞行体验项目详情
     *
     * @param id 项目ID
     * @return 飞行体验项目详情
     */
    @Override
    public FlightExperienceDTO getDetail(Long id) {
        FlightExperience experience = this.getById(id);
        return experience != null ? convertToDTO(experience) : null;
    }

    /**
     * 获取所有上架的飞行体验项目
     *
     * @return 飞行体验项目列表
     */
    @Override
    public List<FlightExperienceDTO> listAllAvailable() {
        LambdaQueryWrapper<FlightExperience> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightExperience::getStatus, 1); // 状态为上架
//        queryWrapper.orderByDesc(FlightExperience::getCreateTime);
        
        List<FlightExperience> list = this.list(queryWrapper);
        return list.stream().map(this::convertToDTO).collect(Collectors.toList());
    }
    
    /**
     * 添加飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 添加后的飞行体验项目
     */
    @Override
    @Transactional
    public FlightExperienceDTO addExperience(FlightExperienceDTO flightExperienceDTO) {
        // 转换为实体
        FlightExperience experience = new FlightExperience();
        BeanUtils.copyProperties(flightExperienceDTO, experience);
        
        // 设置默认值
        if (experience.getStatus() == null) {
            experience.setStatus(0); // 默认下架状态
        }
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        experience.setCreateTime(now);
        experience.setUpdateTime(now);
        
        // 保存到数据库
        boolean success = this.save(experience);
        if (!success) {
            log.error("添加飞行体验项目失败：{}", flightExperienceDTO);
            return null;
        }
        
        // 返回添加后的对象
        return convertToDTO(experience);
    }
    
    /**
     * 更新飞行体验项目
     *
     * @param flightExperienceDTO 飞行体验项目信息
     * @return 更新后的飞行体验项目
     */
    @Override
    @Transactional
    public FlightExperienceDTO updateExperience(FlightExperienceDTO flightExperienceDTO) {
        // 检查项目是否存在
        FlightExperience existingExperience = this.getById(flightExperienceDTO.getId());
        if (existingExperience == null) {
            log.error("更新飞行体验项目失败，项目不存在：id={}", flightExperienceDTO.getId());
            return null;
        }
        
        // 转换为实体
        FlightExperience experience = new FlightExperience();
        BeanUtils.copyProperties(flightExperienceDTO, experience);
        
        // 设置更新时间
        experience.setUpdateTime(LocalDateTime.now());
        
        // 保留创建时间
        experience.setCreateTime(existingExperience.getCreateTime());
        
        // 更新到数据库
        boolean success = this.updateById(experience);
        if (!success) {
            log.error("更新飞行体验项目失败：{}", flightExperienceDTO);
            return null;
        }
        
        // 返回更新后的对象
        return convertToDTO(this.getById(experience.getId()));
    }
    
    /**
     * 删除飞行体验项目
     *
     * @param id 项目ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean deleteExperience(Long id) {
        // 检查项目是否存在
        FlightExperience existingExperience = this.getById(id);
        if (existingExperience == null) {
            log.error("删除飞行体验项目失败，项目不存在：id={}", id);
            return false;
        }
        
        // TODO: 检查是否有关联的订单或预约，如果有则不能删除
        
        // 执行删除
        return this.removeById(id);
    }
    
    /**
     * 修改飞行体验项目状态
     *
     * @param id     项目ID
     * @param status 状态：0下架，1上架
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public boolean changeStatus(Long id, Integer status) {
        // 检查项目是否存在
        FlightExperience existingExperience = this.getById(id);
        if (existingExperience == null) {
            log.error("修改飞行体验项目状态失败，项目不存在：id={}", id);
            return false;
        }
        
        // 检查状态是否有效
        if (status != 0 && status != 1) {
            log.error("修改飞行体验项目状态失败，状态无效：status={}", status);
            return false;
        }
        
        // 如果状态相同，则不需要修改
        if (existingExperience.getStatus().equals(status)) {
            return true;
        }
        
        // 创建更新对象
        FlightExperience updateExperience = new FlightExperience();
        updateExperience.setId(id);
        updateExperience.setStatus(status);
        updateExperience.setUpdateTime(LocalDateTime.now());
        
        // 执行更新
        return this.updateById(updateExperience);
    }
    
    /**
     * 将实体转换为DTO
     */
    private FlightExperienceDTO convertToDTO(FlightExperience experience) {
        if (experience == null) {
            return null;
        }
        
        FlightExperienceDTO dto = new FlightExperienceDTO();
        BeanUtils.copyProperties(experience, dto);
        return dto;
    }
} 