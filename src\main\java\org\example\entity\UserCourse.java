package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_course")
public class UserCourse extends BaseEntity {

    @TableField("user_id")
    private Long userId;
	
    @TableField("total_hours")
    private Double totalHours;
	
    @TableField("completed_hours")
    private Double completedHours;

    @TableField("remaining_hours")
    private Double remainingHours;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
} 