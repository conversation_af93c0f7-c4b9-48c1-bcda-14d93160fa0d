package org.example.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.example.constants.Constants;
import org.example.entity.Appointment;
import org.example.mapper.AppointmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预约自动完成定时任务
 * 每天凌晨自动将前一天的已确认但未完成的预约标记为已完成
 */
@Component
@Slf4j
public class AppointmentCompletionTask {

    @Autowired
    private AppointmentMapper appointmentMapper;

    /**
     * 每天凌晨1点执行自动完成任务
     * 将前一天的已确认(CONFIRMED)但未完成的预约标记为已完成(COMPLETED)
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional
    public void autoCompleteAppointments() {
        log.info("开始执行预约自动完成任务");
        
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        
        // 查询昨天所有状态为CONFIRMED的预约
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Appointment::getAppointmentDate, yesterday)
                   .eq(Appointment::getStatus, Constants.APPOINTMENT_STATUS_CONFIRMED);
        
        List<Appointment> appointments = appointmentMapper.selectList(queryWrapper);
        log.info("找到{}条昨天({}日)已确认但未完成的预约", appointments.size(), yesterday);
        
        if (appointments.isEmpty()) {
            log.info("没有需要自动完成的预约");
            return;
        }
        
        // 批量更新这些预约的状态为COMPLETED
        LambdaUpdateWrapper<Appointment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Appointment::getAppointmentDate, yesterday)
                    .eq(Appointment::getStatus, Constants.APPOINTMENT_STATUS_CONFIRMED)
                    .set(Appointment::getStatus, Constants.APPOINTMENT_STATUS_COMPLETED)
                    .set(Appointment::getUpdateTime, now);
        
        int count = appointmentMapper.update(null, updateWrapper);
        
        log.info("成功自动完成{}条预约", count);
    }
    
    /**
     * 每天凌晨1点30分执行自动取消任务
     * 将前一天的待确认(PENDING)预约标记为已取消(CANCELLED)
     * 这样可以确保那些用户预约后未被确认的记录也能得到妥善处理
     */
    @Scheduled(cron = "0 30 1 * * ?")
    @Transactional
    public void autoCancelPendingAppointments() {
        log.info("开始执行预约自动取消任务");
        
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime now = LocalDateTime.now();
        
        // 查询昨天所有状态为PENDING的预约
        LambdaQueryWrapper<Appointment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Appointment::getAppointmentDate, yesterday)
                   .eq(Appointment::getStatus, Constants.APPOINTMENT_STATUS_PENDING);
        
        List<Appointment> appointments = appointmentMapper.selectList(queryWrapper);
        log.info("找到{}条昨天({}日)待确认但未处理的预约", appointments.size(), yesterday);
        
        if (appointments.isEmpty()) {
            log.info("没有需要自动取消的预约");
            return;
        }
        
        // 批量更新这些预约的状态为CANCELLED
        LambdaUpdateWrapper<Appointment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Appointment::getAppointmentDate, yesterday)
                    .eq(Appointment::getStatus, Constants.APPOINTMENT_STATUS_PENDING)
                    .set(Appointment::getStatus, Constants.APPOINTMENT_STATUS_CANCELLED)
                    .set(Appointment::getUpdateTime, now);
        
        int count = appointmentMapper.update(null, updateWrapper);
        
        log.info("成功自动取消{}条预约", count);
    }
} 