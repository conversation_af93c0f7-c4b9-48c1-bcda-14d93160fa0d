package org.example.config;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SignatureException;
import org.example.utils.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理JWT异常
     */
    @ExceptionHandler({ExpiredJwtException.class, MalformedJwtException.class, SignatureException.class})
    public Result<Void> handleJwtException(Exception e) {
        String message = "认证失败";
        if (e instanceof ExpiredJwtException) {
            message = "登录已过期，请重新登录";
        } else if (e instanceof MalformedJwtException || e instanceof SignatureException) {
            message = "无效的认证信息";
        }
        return Result.error(401, message);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        e.printStackTrace(); // 开发环境打印异常信息
        return Result.error("系统异常，请联系管理员");
    }
} 