package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.User;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 通过微信登录code获取openid
     * @param code 微信登录code
     * @return openid
     */
    String getWxOpenid(String code);
    
    /**
     * 根据openid获取用户信息
     * @param openid 微信openid
     * @return 用户信息
     */
    User getUserByOpenid(String openid);
    
    /**
     * 注册微信用户
     * @param user 用户信息
     * @return 是否成功
     */
    boolean registerWxUser(User user);
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(User user);
    
    /**
     * 根据ID获取用户信息
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);
    
    /**
     * 获取当前登录用户ID
     * @return 用户ID
     */
    Long getCurrentUserId();
    
    /**
     * 获取总用户数
     * @return 用户总数
     */
    int countTotalUsers();
	
	/**
	 * 保存用户
	 */
	boolean saveUser(User user);
	
	/**
	 * 分页查询用户
	 */
	Page<User> pageUsers(int current, int size, String role, String username);
	
	/**
	 * 获取用户详情
	 */
	User getUserDetail(Long id);
	
	/**
	 * 更新用户状态
	 */
	boolean updateUserStatus(Long userId, Integer status);
	
	/**
	 * 删除用户
	 */
	boolean removeUser(Long id);
	
	/**
	 * 更新用户信息
	 */
	boolean updateUser(User user);
	
	/**
	 * 通过用户名查找用户
	 */
	User getUserByUsername(String username);
    
    /**
     * 通过手机号查找用户
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);
    
    /**
     * 注册手机号用户
     * @param user 用户信息
     * @return 是否成功
     */
    boolean registerPhoneUser(User user);
    
    /**
     * 通过微信phoneCode获取用户手机号
     * @param phoneCode 微信手机号授权code
     * @return 手机号
     */
    String getWxPhoneNumber(String phoneCode);
    
    /**
     * 合并两个用户账号
     * @param mainUser 主账号
     * @param secondaryUser 次要账号
     * @return 合并后的用户
     */
    User mergeUsers(User mainUser, User secondaryUser);
}