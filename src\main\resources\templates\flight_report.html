<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞行报告单</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
        }
        .report-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .report-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .table-bordered th, .table-bordered td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .table-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .search-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .time-section th {
            min-width: 80px;
        }
        .rotate-text {
            writing-mode: tb-rl;
            transform: rotate(0deg);
            white-space: nowrap;
            padding: 10px 5px;
            height: 120px;
            vertical-align: middle;
        }
        .narrow-col {
            width: 40px;
        }
        .print-btn {
            margin-left: 10px;
        }
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .page-info {
            margin-right: 15px;
        }
        @media print {
            .search-section, .no-print, .pagination-container {
                display: none;
            }
            .table-bordered th, .table-bordered td {
                border: 1px solid #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 搜索区域 -->
        <div class="search-section">
            <h5>飞行报告单查询</h5>
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">机号</span>
                            </div>
                            <input type="text" class="form-control" id="aircraftNo" name="aircraftNo">
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">日期范围</span>
                            </div>
                            <input type="text" class="form-control datepicker" id="startDate" name="startDate" placeholder="开始日期">
                            <input type="text" class="form-control datepicker" id="endDate" name="endDate" placeholder="结束日期">
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">机长</span>
                            </div>
                            <input type="text" class="form-control" id="captain" name="captain">
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">航线</span>
                            </div>
                            <input type="text" class="form-control" id="route" name="route">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">任务性质</span>
                            </div>
                            <select class="form-control" id="taskType" name="taskType">
                                <option value="">全部</option>
                                <option value="训练">训练</option>
                                <option value="体验">体验</option>
                                <option value="考试">考试</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">时间性质</span>
                            </div>
                            <select class="form-control" id="timeType" name="timeType">
                                <option value="">全部</option>
                                <option value="单飞">单飞</option>
                                <option value="带飞">带飞</option>
                                <option value="教员签字">教员签字</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <button type="submit" class="btn btn-primary">查询</button>
                        <button type="reset" class="btn btn-secondary">重置</button>
                        <button type="button" class="btn btn-success" id="exportBtn">导出</button>
                        <button type="button" class="btn btn-info print-btn" id="printBtn">打印</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 报告单主体 -->
        <div class="report-header">
            <div></div>
            <div class="report-title">飞行报告单</div>
            <div>
                机号: <span id="reportAircraftNo">B-12HC</span> 
                日期: <span id="reportDate"></span>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr class="table-header">
                        <th colspan="2">机组</th>
                        <th colspan="2">航线</th>
                        <th rowspan="2" class="narrow-col">
                            <div class="rotate-text">任务性质</div>
                        </th>
                        <th colspan="4">飞行动态</th>
                        <th colspan="3">飞行时间</th>
                        <th rowspan="2" class="narrow-col">
                            <div class="rotate-text">飞行架次</div>
                        </th>
                        <th colspan="3">时间性质</th>
                    </tr>
                    <tr class="table-header">
                        <th>机长</th>
                        <th>副驾驶</th>
                        <th>起飞站</th>
                        <th>降落站</th>
                        <th>开车时间</th>
                        <th>起飞时间</th>
                        <th>着陆时间</th>
                        <th>关车时间</th>
                        <th>地面时间</th>
                        <th>空中时间</th>
                        <th>合计时间</th>
                        <th>单飞</th>
                        <th>带飞</th>
                        <th>教员签字</th>
                    </tr>
                </thead>
                <tbody id="reportTableBody">
                    <!-- 数据行将通过JavaScript动态生成 -->
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="table-header">
                        <td colspan="8" class="text-right">总计：</td>
                        <td id="totalGroundTime"></td>
                        <td id="totalAirTime"></td>
                        <td id="totalTime"></td>
                        <td id="totalFlights"></td>
                        <td id="totalSoloTime"></td>
                        <td id="totalDualTime"></td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    每页显示:
                    <select id="pageSizeSelect" class="form-control form-control-sm d-inline-block" style="width: auto;">
                        <option value="5">5条</option>
                        <option value="10" selected>10条</option>
                        <option value="20">20条</option>
                        <option value="50">50条</option>
                    </select>
                </div>
                <div class="page-info">
                    显示 <span id="startRecord">1</span> 到 <span id="endRecord">10</span> 条，共 <span id="totalRecords">0</span> 条记录
                </div>
            </div>
            <nav aria-label="飞行报告分页">
                <ul class="pagination mb-0">
                    <li class="page-item disabled" id="prevPageBtn">
                        <a class="page-link" href="javascript:void(0);" aria-label="上一页">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="javascript:void(0);">1</a></li>
                    <li class="page-item"><a class="page-link" href="javascript:void(0);">2</a></li>
                    <li class="page-item"><a class="page-link" href="javascript:void(0);">3</a></li>
                    <li class="page-item" id="nextPageBtn">
                        <a class="page-link" href="javascript:void(0);" aria-label="下一页">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script>
        $(document).ready(function() {
            // 分页相关变量
            var currentPage = 1;
            var pageSize = 10;
            var totalRecords = 0;
            var totalPages = 0;
            var allData = []; // 存储所有数据

            // 设置当前日期
            var today = new Date();
            var dateStr = today.getFullYear() + '-' + 
                          ('0' + (today.getMonth() + 1)).slice(-2) + '-' + 
                          ('0' + today.getDate()).slice(-2);
            $('#reportDate').text(dateStr);
            
            // 初始化日期选择器
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                language: 'zh-CN',
                autoclose: true
            });
            
            // 日期选择器变化时更新报告日期
            $('#startDate').change(function() {
                if ($(this).val()) {
                    $('#reportDate').text($(this).val());
                }
            });
            
            // 重置按钮点击时恢复当前日期
            $('button[type="reset"]').click(function() {
                $('#reportDate').text(dateStr);
            });
            
            // 打印功能
            $('#printBtn').click(function() {
                window.print();
            });
            
            // 导出功能
            $('#exportBtn').click(function() {
                alert('导出功能将在后端实现');
            });
            
            // 表单提交处理
            $('#searchForm').submit(function(e) {
                e.preventDefault();
                currentPage = 1; // 重置为第一页
                
                // 更新报告日期显示
                var startDateVal = $('#startDate').val();
                var endDateVal = $('#endDate').val();
                
                if (startDateVal && endDateVal) {
                    // 如果开始和结束日期都有，显示日期范围
                    $('#reportDate').text(startDateVal + ' 至 ' + endDateVal);
                } else if (startDateVal) {
                    // 只有开始日期
                    $('#reportDate').text(startDateVal);
                } else if (endDateVal) {
                    // 只有结束日期
                    $('#reportDate').text('截至 ' + endDateVal);
                } else {
                    // 都没有则显示当天日期
                    $('#reportDate').text(dateStr);
                }
                
                // 更新机号显示
                var aircraftNoVal = $('#aircraftNo').val();
                if (aircraftNoVal) {
                    $('#reportAircraftNo').text(aircraftNoVal);
                } else {
                    $('#reportAircraftNo').text('B-12HC');
                }
                
                searchReports();
            });

            // 重置按钮点击时恢复默认值
            $('button[type="reset"]').click(function() {
                $('#reportDate').text(dateStr);
                $('#reportAircraftNo').text('B-12HC');
            });

            // 每页显示条数变化
            $('#pageSizeSelect').change(function() {
                pageSize = parseInt($(this).val());
                currentPage = 1; // 重置为第一页
                renderTable();
            });

            // 上一页按钮点击事件
            $('#prevPageBtn').click(function() {
                if ($(this).hasClass('disabled')) return;
                currentPage--;
                renderTable();
            });

            // 下一页按钮点击事件
            $('#nextPageBtn').click(function() {
                if ($(this).hasClass('disabled')) return;
                currentPage++;
                renderTable();
            });

            // 页码点击事件
            $(document).on('click', '.page-item:not(.disabled)', function() {
                if ($(this).attr('id') === 'prevPageBtn' || $(this).attr('id') === 'nextPageBtn') return;
                currentPage = parseInt($(this).text());
                renderTable();
            });

            // 搜索飞行报告
            function searchReports() {
                // 这里应该发送AJAX请求到后端获取数据
                // 示例代码：
                /*
                $.ajax({
                    url: '/api/flight-reports/search',
                    type: 'POST',
                    data: $('#searchForm').serialize(),
                    success: function(response) {
                        allData = response.data;
                        totalRecords = allData.length;
                        totalPages = Math.ceil(totalRecords / pageSize);
                        renderTable();
                    },
                    error: function(error) {
                        console.error('查询失败:', error);
                    }
                });
                */
                
                // 模拟数据
                var mockData = [];
                // 生成30条模拟数据
                for (var i = 1; i <= 30; i++) {
                    mockData.push({
                        id: i,
                        captain: '张三' + i,
                        copilot: i % 3 === 0 ? '李四' : '王五',
                        departureStation: i % 2 === 0 ? '北京' : '上海',
                        arrivalStation: i % 2 === 0 ? '上海' : '广州',
                        taskType: i % 3 === 0 ? '训练' : (i % 3 === 1 ? '体验' : '考试'),
                        startupTime: '0' + (8 + i % 10) + ':00',
                        takeoffTime: '0' + (8 + i % 10) + ':15',
                        landingTime: (9 + i % 10) + ':45',
                        shutdownTime: (10 + i % 10) + ':00',
                        groundTime: '0:30',
                        airTime: '1:' + (30 + i % 30),
                        totalTime: '2:' + (i % 60),
                        flightCount: '1',
                        soloTime: i % 2 === 0 ? '2:00' : '',
                        dualTime: i % 2 === 1 ? '2:00' : '',
                        instructorSignature: i % 2 === 1 ? '王教员' : ''
                    });
                }
                
                allData = mockData;
                totalRecords = allData.length;
                totalPages = Math.ceil(totalRecords / pageSize);
                renderTable();
            }

            // 渲染表格和分页
            function renderTable() {
                // 计算当前页的数据
                var startIndex = (currentPage - 1) * pageSize;
                var endIndex = Math.min(startIndex + pageSize, totalRecords);
                var currentPageData = allData.slice(startIndex, endIndex);
                
                // 更新表格内容
                updateReportTable(currentPageData);
                
                // 更新分页信息
                updatePagination();
            }

            // 更新分页控件
            function updatePagination() {
                // 更新显示记录信息
                var startRecord = totalRecords === 0 ? 0 : (currentPage - 1) * pageSize + 1;
                var endRecord = Math.min(currentPage * pageSize, totalRecords);
                $('#startRecord').text(startRecord);
                $('#endRecord').text(endRecord);
                $('#totalRecords').text(totalRecords);
                
                // 更新分页按钮
                var $pagination = $('.pagination');
                $pagination.empty();
                
                // 上一页按钮
                var $prevBtn = $('<li class="page-item' + (currentPage === 1 ? ' disabled' : '') + '" id="prevPageBtn">' +
                                '<a class="page-link" href="javascript:void(0);" aria-label="上一页">' +
                                '<span aria-hidden="true">&laquo;</span></a></li>');
                $pagination.append($prevBtn);
                
                // 计算显示的页码范围
                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(totalPages, startPage + 4);
                
                // 如果页码不足5个，调整起始页
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }
                
                // 添加页码按钮
                for (var i = startPage; i <= endPage; i++) {
                    var $pageBtn = $('<li class="page-item' + (i === currentPage ? ' active' : '') + '">' +
                                    '<a class="page-link" href="javascript:void(0);">' + i + '</a></li>');
                    $pagination.append($pageBtn);
                }
                
                // 下一页按钮
                var $nextBtn = $('<li class="page-item' + (currentPage === totalPages || totalPages === 0 ? ' disabled' : '') + '" id="nextPageBtn">' +
                                '<a class="page-link" href="javascript:void(0);" aria-label="下一页">' +
                                '<span aria-hidden="true">&raquo;</span></a></li>');
                $pagination.append($nextBtn);
                
                // 重新绑定事件
                $('#prevPageBtn').click(function() {
                    if ($(this).hasClass('disabled')) return;
                    currentPage--;
                    renderTable();
                });
                
                $('#nextPageBtn').click(function() {
                    if ($(this).hasClass('disabled')) return;
                    currentPage++;
                    renderTable();
                });
                
                $('.page-item:not(#prevPageBtn):not(#nextPageBtn)').click(function() {
                    currentPage = parseInt($(this).text());
                    renderTable();
                });
            }
            
            // 更新报告表格
            function updateReportTable(data) {
                var tbody = $('#reportTableBody');
                tbody.empty();
                
                var totalGroundTime = 0;
                var totalAirTime = 0;
                var totalTime = 0;
                var totalFlights = 0;
                var totalSoloTime = 0;
                var totalDualTime = 0;
                
                if (data.length === 0) {
                    // 如果没有数据，显示一个空行
                    var emptyRow = $('<tr></tr>');
                    emptyRow.append('<td colspan="16" class="text-center">没有找到符合条件的记录</td>');
                    tbody.append(emptyRow);
                } else {
                    data.forEach(function(item) {
                        var row = $('<tr></tr>');
                        row.append('<td>' + (item.captain || '') + '</td>');
                        row.append('<td>' + (item.copilot || '') + '</td>');
                        row.append('<td>' + (item.departureStation || '') + '</td>');
                        row.append('<td>' + (item.arrivalStation || '') + '</td>');
                        row.append('<td>' + (item.taskType || '') + '</td>');
                        row.append('<td>' + (item.startupTime || '') + '</td>');
                        row.append('<td>' + (item.takeoffTime || '') + '</td>');
                        row.append('<td>' + (item.landingTime || '') + '</td>');
                        row.append('<td>' + (item.shutdownTime || '') + '</td>');
                        row.append('<td>' + (item.groundTime || '') + '</td>');
                        row.append('<td>' + (item.airTime || '') + '</td>');
                        row.append('<td>' + (item.totalTime || '') + '</td>');
                        row.append('<td>' + (item.flightCount || '') + '</td>');
                        row.append('<td>' + (item.soloTime || '') + '</td>');
                        row.append('<td>' + (item.dualTime || '') + '</td>');
                        row.append('<td>' + (item.instructorSignature || '') + '</td>');
                        
                        tbody.append(row);
                        
                        // 计算总计
                        totalFlights += parseInt(item.flightCount || 0);
                        
                        // 注意：这里简化处理，实际应该解析时间格式并正确计算
                        // 这里仅作为示例
                    });
                }
                
                // 更新总计行
                $('#totalGroundTime').text(totalGroundTime + ':00');
                $('#totalAirTime').text(totalAirTime + ':30');
                $('#totalTime').text(totalTime + ':30');
                $('#totalFlights').text(totalFlights);
                $('#totalSoloTime').text('2:30');
                $('#totalDualTime').text('2:00');
            }

            // 初始加载
            searchReports();
        });
    </script>
</body>
</html> 