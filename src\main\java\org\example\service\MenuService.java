package org.example.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.example.entity.Menu;

import java.util.List;

/**
 * 菜单服务接口
 */
public interface MenuService extends IService<Menu> {
    
    /**
     * 获取所有菜单列表
     */
    List<Menu> getAllMenus();
    
    /**
     * 获取菜单树形结构
     */
    List<Menu> getMenuTree();
    
    /**
     * 根据角色ID获取菜单列表
     */
    List<Menu> getMenusByRoleId(Long roleId);
    
    /**
     * 根据角色ID获取菜单树形结构
     */
    List<Menu> getMenuTreeByRoleId(Long roleId);
    
    /**
     * 根据用户ID获取菜单列表
     */
    List<Menu> getMenusByUserId(Long userId);
    
    /**
     * 根据用户ID获取菜单树形结构
     */
    List<Menu> getMenuTreeByUserId(Long userId);
    
    /**
     * 保存菜单
     */
    boolean saveMenu(Menu menu);
    
    /**
     * 更新菜单
     */
    boolean updateMenu(Menu menu);
    
    /**
     * 删除菜单
     */
    boolean deleteMenu(Long id);
} 