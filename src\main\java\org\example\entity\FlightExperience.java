package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行体验项目实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_experience")
public class FlightExperience extends BaseEntity {
    /**
     * 体验项目名称
     */
    private String name;
    
    /**
     * 体验项目描述
     */
    private String description;
    
    /**
     * 飞行时长(小时)
     */
    private Integer duration;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 封面图片URL
     */
    private String coverImage;
    
    /**
     * 状态:0下架,1上架
     */
    private Integer status;
    
    /**
     * 适合人群
     */
    private String suitableFor;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 