package org.example.controller.admin;

import jakarta.servlet.http.HttpServletRequest;
import org.example.entity.Menu;
import org.example.service.MenuService;
import org.example.utils.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 菜单控制器
 */
@RestController
@RequestMapping("/admin/menus")
public class MenuController {
    
    private static final Logger logger = LoggerFactory.getLogger(MenuController.class);
    
    @Autowired
    private MenuService menuService;
    
    /**
     * 获取所有菜单（树形结构）
     */
    @GetMapping
    public Result<List<Menu>> getAllMenus() {
        List<Menu> menus = menuService.getMenuTree();
        return Result.success(menus);
    }
    
    /**
     * 获取菜单详情
     */
    @GetMapping("/{id}")
    public Result<Menu> getMenuDetail(@PathVariable Long id) {
        Menu menu = menuService.getById(id);
        return menu != null ? Result.success(menu) : Result.error("菜单不存在");
    }
    
    /**
     * 创建菜单
     */
    @PostMapping
    public Result<Menu> createMenu(@RequestBody Menu menu) {
        try {
            // 确保parentId不为null，默认为0（顶级菜单）
            if (menu.getParentId() == null) {
                menu.setParentId(0L);
            }
            
            boolean success = menuService.saveMenu(menu);
            return success ? Result.success(menu) : Result.error("创建菜单失败");
        } catch (Exception e) {
            logger.error("创建菜单异常: ", e);
            return Result.error("创建菜单异常: " + e.getMessage());
        }
    }
    
    /**
     * 更新菜单
     */
    @PutMapping
    public Result<Menu> updateMenu(@RequestBody Menu menu) {
        if (menu.getId() == null) {
            return Result.error("菜单ID不能为空");
        }
        boolean success = menuService.updateMenu(menu);
        return success ? Result.success(menu) : Result.error("更新菜单失败");
    }
    
    /**
     * 删除菜单
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteMenu(@PathVariable Long id) {
        boolean success = menuService.deleteMenu(id);
        return success ? Result.success(true) : Result.error("删除菜单失败，可能存在子菜单");
    }
    
    /**
     * 获取当前用户的菜单（树形结构）
     */
    @GetMapping("/user")
    public Result<List<Menu>> getUserMenus(HttpServletRequest request) {
        // 从请求属性中获取JWT解析出的用户ID
        Object userIdObj = request.getAttribute("userId");
        if (userIdObj == null) {
            return Result.error("未获取到用户ID");
        }
        
        Long userId = Long.parseLong(userIdObj.toString());
        
        // 获取原始菜单列表（未构建树形结构）
        List<Menu> originalMenus = menuService.getMenusByUserId(userId);
        for (Menu menu : originalMenus) {
            // 确保parentId不为null
            if (menu.getParentId() == null) {
                menu.setParentId(0L);
            }
        }
        
        // 使用getMenuTreeByUserId而不是getMenusByUserId，确保返回树形结构
        List<Menu> menus = menuService.getMenuTreeByUserId(userId);
        
        // 打印树形结构菜单
        printMenuTree(menus, 0);
        
        // 检查菜单数据是否正确序列化
        try {
            ObjectMapper mapper = new ObjectMapper();
            String jsonMenus = mapper.writeValueAsString(menus);
        } catch (Exception e) {
        }
        
        return Result.success(menus);
    }
    
    /**
     * 递归打印菜单树结构（用于调试）
     */
    private void printMenuTree(List<Menu> menus, int level) {
        if (menus == null || menus.isEmpty()) {
            return;
        }
        
        String indent = "  ".repeat(level);
        for (Menu menu : menus) {
            if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
                printMenuTree(menu.getChildren(), level + 1);
            }
        }
    }
    
    /**
     * 根据角色ID获取菜单（树形结构）
     */
    @GetMapping("/role/{roleId}")
    public Result<List<Menu>> getMenusByRoleId(@PathVariable Long roleId) {
        List<Menu> menus = menuService.getMenuTreeByRoleId(roleId);
        return Result.success(menus);
    }
} 