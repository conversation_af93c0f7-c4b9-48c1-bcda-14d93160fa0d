package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("time_slot")
public class TimeSlot extends BaseEntity {

    @TableField("date")
    private LocalDate date;

    @TableField("start_time")
    private LocalTime startTime;

    @TableField("end_time")
    private LocalTime endTime;

    @TableField("status")
    private Integer status; // 1-可用 0-不可用

    @TableField("purpose")
    private String purpose; // ALL, EXPERIENCE_ONLY, TRAINING_ONLY
    
    @TableField("experience_capacity")
    private Integer experienceCapacity; // 体验预约容量
    
    @TableField("training_capacity")
    private Integer trainingCapacity; // 训练预约容量
    
    @TableField("experience_booked_count")
    private Integer experienceBookedCount; // 已预约的体验数量
    
    @TableField("training_booked_count")
    private Integer trainingBookedCount; // 已预约的训练数量

    @TableField("description")
    private String description;
} 