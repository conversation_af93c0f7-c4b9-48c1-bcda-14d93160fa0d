package org.example.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.example.dto.AircraftDTO;
import org.example.entity.Aircraft;
import org.example.service.AircraftService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞机控制器
 */
@RestController
@RequestMapping("/aircraft")
@Slf4j
public class AircraftController {

    @Autowired
    private AircraftService aircraftService;

    /**
     * 分页查询飞机列表
     *
     * @param page   页码
     * @param size   每页大小
     * @param model  机型，可选
     * @param type   类型，可选
     * @param status 状态，可选
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Page<AircraftDTO>> page(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String model,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {

        log.info("分页查询飞机列表：page={}, size={}, model={}, type={}, status={}", page, size, model, type, status);
        Page<Aircraft> pageInfo = new Page<>(page, size);
        Page<AircraftDTO> result = aircraftService.page(pageInfo, model, type, status);
        return Result.success(result);
    }

    /**
     * 获取飞机详情
     *
     * @param id 飞机ID
     * @return 飞机详情
     */
    @GetMapping("/{id}")
    public Result<AircraftDTO> getDetail(@PathVariable Long id) {
        log.info("获取飞机详情：id={}", id);
        AircraftDTO detail = aircraftService.getDetail(id);
        return Result.success(detail);
    }
	
} 