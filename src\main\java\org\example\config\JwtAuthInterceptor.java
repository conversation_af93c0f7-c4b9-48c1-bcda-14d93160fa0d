package org.example.config;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.example.utils.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Date;

/**
 * JWT认证拦截器
 * 用于验证请求中的JWT令牌
 */
@Component
public class JwtAuthInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthInterceptor.class);
    
    @Autowired
    private JwtProperties jwtProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求信息
        String requestURI = request.getRequestURI();
        String requestMethod = request.getMethod();
        String contentType = request.getContentType();
        
        logger.info("========== 请求拦截详情 ==========");
        logger.info("请求URI: {}", requestURI);
        logger.info("请求方法: {}", requestMethod);
        logger.info("Content-Type: {}", contentType);
        logger.info("请求参数: {}", request.getQueryString());
        logger.info("Authorization: {}", request.getHeader("Authorization"));
        logger.info("=================================");
        
        // 对登录和其他公开接口放行
        if (requestURI.contains("/admin/login") || 
            requestURI.contains("/auth/") || 
            requestURI.contains("/api/public/") || 
            requestURI.contains("/swagger") ||
            requestURI.contains("/v3/api-docs") ||
            requestURI.contains("/wx/user/login") ||
            requestURI.contains("/wx/user/wxPhoneLogin") ||
            requestURI.contains("/wx/user/phone/login") ||
            requestURI.contains("/wx/user/sms/send")) {
            logger.info("公开接口，无需验证token，已放行: {}", requestURI);
            return true;
        }

        // 获取请求头中的token
        String header = jwtProperties.getHeader();
        String token = request.getHeader(header);
        logger.info("从请求头[{}]中获取到的token: {}", header, token);
        
        if (token == null) {
            logger.error("Authorization头为空");
            sendUnauthorizedResponse(response, "未提供token");
            return false;
        }
        
        // 保存原始token用于调试
        String originalToken = token;
        
        // 处理可能的Bearer前缀
        String tokenPrefix = jwtProperties.getTokenPrefix();
        if (StringUtils.hasText(tokenPrefix) && token.startsWith(tokenPrefix)) {
            logger.info("Token包含前缀[{}]，移除前缀", tokenPrefix);
            token = token.substring(tokenPrefix.length()).trim();
            logger.info("处理后的token: [{}]，长度: {}", token, token.length());
        } else {
            // 如果没有前缀，也确保没有首尾空格
            token = token.trim();
            logger.info("处理后的token: [{}]，长度: {}", token, token.length());
        }
        
        // 验证token是否有效
        try {
            logger.info("开始验证token");
            Claims claims = JwtUtil.parseToken(token);
            
            if (claims == null) {
                logger.error("Token解析失败，无法获取claims");
                
                // 尝试直接使用原始token（包含Bearer前缀）
                logger.info("尝试直接使用原始token进行解析");
                claims = JwtUtil.parseToken(originalToken);
                
                if (claims == null) {
                    logger.error("原始token也解析失败，无法获取claims");
                    sendUnauthorizedResponse(response, "Token无效");
                    return false;
                } else {
                    logger.info("原始token解析成功");
                }
            }
            
            logger.info("Token解析成功，用户ID: {}, 用户名: {}, 角色: {}", 
                claims.get("userId"), claims.get("username"), claims.get("role"));
            
            if (claims.getExpiration().before(new Date())) {
                logger.error("Token已过期，过期时间: {}", claims.getExpiration());
                sendUnauthorizedResponse(response, "Token已过期");
                return false;
            }
            
            // 将用户信息存入request属性
            request.setAttribute("userId", claims.get("userId"));
            request.setAttribute("username", claims.get("username"));
            request.setAttribute("role", claims.get("role"));
            logger.info("Token验证成功，请求继续处理");
            return true;
            
        } catch (ExpiredJwtException e) {
            logger.error("Token已过期: {}", e.getMessage());
            sendUnauthorizedResponse(response, "Token已过期");
        } catch (MalformedJwtException e) {
            logger.error("Token格式错误: {}", e.getMessage());
            sendUnauthorizedResponse(response, "Token格式错误");
        } catch (SignatureException e) {
            logger.error("Token签名验证失败: {}", e.getMessage());
            sendUnauthorizedResponse(response, "Token签名验证失败");
        } catch (Exception e) {
            logger.error("Token验证异常: {}", e.getMessage(), e);
            sendUnauthorizedResponse(response, "Token验证失败: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 发送未授权响应
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws Exception {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":401,\"msg\":\"未授权或token已过期: " + message + "\",\"data\":null}");
    }
} 