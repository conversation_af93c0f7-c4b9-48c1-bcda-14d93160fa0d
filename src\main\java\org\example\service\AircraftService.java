package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.example.dto.AircraftDTO;
import org.example.entity.Aircraft;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞机服务接口
 */
public interface AircraftService extends IService<Aircraft> {

    /**
     * 分页获取飞机列表
     *
     * @param page  分页参数
     * @param model 机型，可选
     * @param type  类型，可选
     * @param status 状态，可选
     * @return 分页结果
     */
    Page<AircraftDTO> page(Page<Aircraft> page, String model, String type, String status);

    /**
     * 根据ID获取飞机详情
     *
     * @param id 飞机ID
     * @return 飞机详情
     */
    AircraftDTO getDetail(Long id);
	
} 