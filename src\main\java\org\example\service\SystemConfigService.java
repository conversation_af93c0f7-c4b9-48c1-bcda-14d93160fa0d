package org.example.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.example.entity.SystemConfig;

/**
 * 系统配置Service接口
 */
public interface SystemConfigService extends IService<SystemConfig> {
    
    /**
     * 根据键获取配置值
     * @param key 配置键
     * @return 配置值
     */
    String getConfigValue(String key);
    
    /**
     * 设置配置值
     * @param key 配置键
     * @param value 配置值
     * @param description 描述
     * @return 是否成功
     */
    boolean setConfigValue(String key, String value, String description);
    
    /**
     * 检查预约功能是否开启
     * @return 是否开启
     */
    boolean isAppointmentEnabled();
    
    /**
     * 设置预约功能状态
     * @param enabled 是否开启
     * @return 是否成功
     */
    boolean setAppointmentEnabled(boolean enabled);
} 