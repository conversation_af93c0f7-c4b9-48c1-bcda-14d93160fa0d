package org.example.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.example.entity.Menu;

import java.util.List;

/**
 * 菜单Mapper接口
 */
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {
    
    /**
     * 获取所有菜单列表
     */
    @Select("SELECT id, parent_id as parentId, name, url, type, icon, sort, iframe, menu, status FROM sys_menu ORDER BY sort ASC")
    List<Menu> getAllMenus();
    
    /**
     * 获取角色的菜单ID列表
     */
    @Select("SELECT menu_id FROM sys_role_menu WHERE role_id = #{roleId}")
    List<Long> getMenuIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 获取角色的菜单列表
     */
    @Select("SELECT m.id, m.parent_id as parentId, m.name, m.url, m.type, m.icon, m.sort, m.iframe, m.menu, m.status FROM sys_menu m " +
            "INNER JOIN sys_role_menu rm ON m.id = rm.menu_id " +
            "WHERE rm.role_id = #{roleId} " +
            "ORDER BY m.sort ASC")
    List<Menu> getMenusByRoleId(@Param("roleId") Long roleId);
} 