<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.mapper.TrainingAppointmentMapper">
    
    <!-- 统计某个时间段在某一天的预约数量 -->
    <select id="countByTimeSlotAndDate" resultType="int">
        SELECT COUNT(*)
        FROM training_appointment
        WHERE time_slot_id = #{timeSlotId}
          AND appointment_date = #{date}
          AND status != 'CANCELLED'
    </select>
    
</mapper> 