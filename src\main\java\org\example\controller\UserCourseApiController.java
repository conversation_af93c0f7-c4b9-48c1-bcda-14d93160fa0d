package org.example.controller;

import org.example.dto.UserCourseDTO;
import org.example.entity.User;
import org.example.entity.UserCourse;
import org.example.service.UserCourseService;
import org.example.service.UserService;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户端课时信息API控制器
 */
@RestController
@RequestMapping("/wx/user-course")
public class UserCourseApiController {
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private UserCourseService userCourseService;
	
	/**
	 * 获取用户课程信息
	 */
	@GetMapping("/info")
	public Result<UserCourseDTO> getUserCourseInfo() {
		try {
			// 从JWT中获取当前用户ID
			Long userId = userService.getCurrentUserId();
			if (userId == null) {
				return Result.error("用户未登录");
			}
			
			// 根据用户ID获取课程信息
			UserCourseDTO courseInfo = userCourseService.getUserCourseInfo(userId);
			
			if (courseInfo == null) {
				// 返回默认的空课程信息
				courseInfo = new UserCourseDTO();
				courseInfo.setTotalHours(0.00);
				courseInfo.setCompletedHours(0.00);
				courseInfo.setRemainingHours(0.00);
			}
			
			return Result.success(courseInfo);
		} catch (Exception e) {
			e.printStackTrace();
			return Result.error("获取课程信息失败: " + e.getMessage());
		}
	}
}