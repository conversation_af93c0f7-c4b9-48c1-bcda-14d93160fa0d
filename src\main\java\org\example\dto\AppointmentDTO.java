package org.example.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 预约DTO
 */
@Data
public class AppointmentDTO {
    /**
     * 预约ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 预约日期
     */
    private LocalDate appointmentDate;
    
    /**
     * 时间段ID
     */
    private Long timeSlotId;
    
    /**
     * 时间段信息
     */
    private TimeSlotDTO timeSlot;
    
    /**
     * 飞机ID
     */
    private Long aircraftId;
    
    /**
     * 飞机信息
     */
    private AircraftDTO aircraft;
    

    
    /**
     * 体验项目ID
     */
    private Long experienceId;
    
    /**
     * 体验项目信息
     */
    private FlightExperienceDTO experience;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
} 