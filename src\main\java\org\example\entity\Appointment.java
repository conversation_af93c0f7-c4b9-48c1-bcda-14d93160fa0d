package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("appointment")
public class Appointment extends BaseEntity {

    @TableField("user_id")
    private Long userId;

    @TableField("appointment_date")
    private LocalDate appointmentDate;

    @TableField("time_slot_id")
    private Long timeSlotId;
    
    @TableField("start_time")
    private LocalTime startTime;
    
    @TableField("end_time")
    private LocalTime endTime;
    
    @TableField("time_slot_purpose")
    private String timeSlotPurpose;
	
    @TableField("status")
    private String status;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("remark")
    private String remark;
} 