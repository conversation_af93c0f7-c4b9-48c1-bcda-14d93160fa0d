package org.example.command;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.example.entity.TimeSlot;
import org.example.service.AppointmentService;
import org.example.service.TimeSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 时间段清理命令行工具
 * 用于手动执行时间段清理任务和数据迁移
 */
@Component
public class TimeSlotCleanupCommand implements CommandLineRunner {

    @Autowired
    private TimeSlotService timeSlotService;
    
    @Autowired
    private AppointmentService appointmentService;

    @Override
    public void run(String... args) throws Exception {
        // 命令行参数解析
        if (args.length > 0) {
            String command = args[0];
            
            switch (command) {
                case "cleanup":
                    // 清理过期时间段
                    cleanupExpiredTimeSlots();
                    break;
                case "sync":
                    // 同步时间段信息到预约记录
                    syncTimeSlotInfo();
                    break;
                case "help":
                default:
                    printHelp();
                    break;
            }
        }
    }
    
    /**
     * 清理过期时间段
     */
    private void cleanupExpiredTimeSlots() {
        System.out.println("开始清理过期时间段...");
        
        LocalDate today = LocalDate.now();
        
        // 获取过期的时间段
        LambdaQueryWrapper<TimeSlot> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(TimeSlot::getDate, today);
        List<TimeSlot> expiredTimeSlots = timeSlotService.list(wrapper);
        
        System.out.println("找到 " + expiredTimeSlots.size() + " 个过期时间段");
        
        // 执行清理
        boolean success = timeSlotService.remove(wrapper);
        
        if (success) {
            System.out.println("过期时间段清理成功");
        } else {
            System.out.println("过期时间段清理失败");
        }
    }
    
    /**
     * 同步时间段信息到预约记录
     */
    private void syncTimeSlotInfo() {
        System.out.println("开始同步时间段信息到预约记录...");
        
        int count = appointmentService.syncAllAppointmentsTimeSlotInfo();
        
        System.out.println("成功同步 " + count + " 条预约记录的时间段信息");
    }
    
    /**
     * 打印帮助信息
     */
    private void printHelp() {
        System.out.println("时间段管理工具使用说明：");
        System.out.println("  cleanup - 清理过期时间段");
        System.out.println("  sync    - 同步时间段信息到预约记录");
        System.out.println("  help    - 显示帮助信息");
    }
} 