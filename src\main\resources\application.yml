server:
  port: 6039
  address: 0.0.0.0  # 监听所有IP地址

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************
    username: root
    password: root
    
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
  
  # 返回json的全局时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  
#mybatis:
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:/mapper/**.xml
  global-config:
    db-config:
      id-type: auto # id生成策略类型

# JWT配置
jwt:
  # JWT加解密使用的密钥
  secret: PlaneManagementSecretKey_MustBe_At_Least_32_Bytes_Long
  # token有效期（单位：小时）
  expire-time: 12
  # token前缀
  token-prefix: 
  # token请求头名称
  header: Authorization

# 阿里短信配置
aliyun:
  
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5t7pWeuuaicAWM6iMFwM
    secret: ******************************
    bucket: travel-suzimo
  
  # 阿里云短信配置
  sms:
    regionId: cn-hangzhou
    accessKeyId: LTAI5t7pWeuuaicAWM6iMFwM
    secret: ******************************

# 微信小程序配置
wx:
  appid: wx362e82b25540b0b8  # 替换为你的微信小程序AppID
  secret: c284025ef943499a3fc5ca853ed64276 # 替换为你的微信小程序AppSecret