package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user")
public class User extends BaseEntity {

    @TableField("openid")
    private String openid;
	
	@TableField("nickname")
	private String nickname;  // 微信昵称

    @TableField("username")
    private String username;

    @TableField("phone")
    private String phone;

    @TableField("avatar")
    private String avatar;

    @TableField("role")
    private String role;

    @TableField("student_type")
    private String studentType; // SPORT_LICENSE, PRIVATE_LICENSE, SPORT_INSTRUCTOR

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("status")
    private Integer status;
} 