package org.example.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 通用订单DTO
 */
@Data
public class GenericOrderDTO {
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 商品类型: EXPERIENCE(体验), COURSE(课程), PACKAGE(套餐)
     */
    private String productType;
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * 商品金额
     */
    private BigDecimal amount;
    
    /**
     * 商品名称
     */
    private String productName;
} 