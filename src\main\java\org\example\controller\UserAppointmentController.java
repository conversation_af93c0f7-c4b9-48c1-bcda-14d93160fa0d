package org.example.controller;

import org.example.dto.AppointmentDTO;
import org.example.entity.Appointment;
import org.example.service.AppointmentService;
import org.example.service.SystemConfigService;
import org.example.utils.JwtUtil;
import org.example.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户端预约控制器
 */
@RestController
@RequestMapping("/wx/appointment")
public class UserAppointmentController {

    @Autowired
    private AppointmentService appointmentService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    /**
     * 创建预约
     */
    @PostMapping("/create")
    public Result<Appointment> createAppointment(@RequestBody AppointmentDTO appointmentDTO, 
                                             @RequestHeader("Authorization") String token) {
        // 检查预约功能是否开启
        if (!systemConfigService.isAppointmentEnabled()) {
            return Result.error("预约功能暂时关闭，请稍后再试");
        }
		
        // 从token中解析用户ID
        Long userId = JwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.error("用户未登录或token无效");
        }
        
        // 创建Appointment对象并从DTO复制属性
	    Appointment appointment = new Appointment();
        appointment.setUserId(userId);
        appointment.setAppointmentDate(appointmentDTO.getAppointmentDate());
        appointment.setTimeSlotId(appointmentDTO.getTimeSlotId());
        appointment.setRemark(appointmentDTO.getRemark());
        // 设置初始状态为待确认
        appointment.setStatus("PENDING");
	
	    // 检查用户是否在同一时间段已有预约
	    if (appointmentService.hasUserAppointmentAtTimeSlot(
			    appointment.getUserId(), appointment.getAppointmentDate(), appointment.getTimeSlotId())) {
		    return Result.error("您已在该时间段有预约，请勿重复预约");
	    }
	
	    // 创建预约
        try {
            boolean success = appointmentService.createAppointment(appointment);
            if (success) {
                return Result.success(appointment);
            } else {
                return Result.error("创建预约失败");
            }
        } catch (Exception e) {
            return Result.error("创建预约失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的预约列表
     */
    @GetMapping("/list")
    public Result<List<AppointmentDTO>> getUserAppointments(@RequestParam(required = false) String status,
                                                    @RequestHeader("Authorization") String token) {
        // 从token中解析用户ID
        Long userId = JwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.error("用户未登录或token无效");
        }
        
        try {
            // 获取用户预约列表
            List<AppointmentDTO> appointments;
            // 获取所有预约
            appointments = appointmentService.getUserAppointmentDTOs(userId);
            
            // 如果提供了状态，使用Java流进行过滤
            if (status != null && !status.isEmpty()) {
                appointments = appointments.stream()
                    .filter(dto -> status.equals(dto.getStatus()))
                    .toList();
            }
            
            return Result.success(appointments);
        } catch (Exception e) {
            return Result.error("获取预约列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消预约
     */
    @PostMapping("/cancel/{id}")
    public Result<Boolean> cancelAppointment(@PathVariable Long id,
                                       @RequestHeader("Authorization") String token) {
        // 从token中解析用户ID
        Long userId = JwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.error("用户未登录或token无效");
        }
        
        // 先检查预约是否属于该用户
        try {
            Appointment appointment = appointmentService.getAppointment(id);
            if (appointment == null) {
                return Result.error("预约不存在");
            }
            
            if (!userId.equals(appointment.getUserId())) {
                return Result.error("无权操作此预约");
            }
            
            // 取消预约
            boolean success = appointmentService.cancelAppointment(id);
            if (success) {
                return Result.success(true, "取消预约成功");
            } else {
                return Result.error("取消预约失败，可能是预约已完成或状态不允许取消");
            }
        } catch (Exception e) {
            return Result.error("取消预约失败: " + e.getMessage());
        }
    }
	
	/**
	 * 获取预约详情
	 *
	 * @param id 预约ID
	 * @return 预约详情
	 */
	@GetMapping("/detail/{id}")
	public Result<AppointmentDTO> getDetail(@PathVariable Long id) {
		AppointmentDTO detail = appointmentService.getAppointmentDTO(id);
		return detail != null ? Result.success(detail) : Result.error("预约不存在");
	}
	
}