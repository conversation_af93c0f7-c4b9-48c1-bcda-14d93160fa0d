package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.FlightReport;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞行报告服务接口
 */
public interface FlightReportService {
    
    /**
     * 创建飞行报告
     * 
     * @param flightReport 飞行报告信息
     *                    - userCourseId: 学员课时记录ID（可选，如不提供则通过姓名查找）
     *                    - pilotName: 主驾驶姓名（必填）
     *                    - copilotName: 副驾驶姓名（必填）
     *                    - departureAirport: 起飞机场（必填）
     *                    - landingAirport: 降落机场（必填）
     *                    - trainingType: 训练类型（必填）
     *                    - flightDate: 飞行日期（可选，默认当天）
     *                    - startupTime: 开车时间（必填，格式：时:分）
     *                    - takeoffTime: 起飞时间（必填，格式：时:分）
     *                    - landingTime: 着陆时间（必填，格式：时:分）
     *                    - shutdownTime: 关车时间（必填，格式：时:分）
     *                    - landingCount: 着陆次数（必填）
     *                    - totalHours: 飞行总时间（自动计算）
     *                    - reportStatus: 报告状态（可选，默认"未提交"）
     *                    - remarks: 备注
     * @return 操作结果
     * 
     * 业务规则：
     * - 自动计算空中时间（起飞时间到着陆时间）
     * - 自动计算地面时间（开车到起飞 + 着陆到关车）
     * - 自动计算合计时间（空中时间 + 地面时间）
     * - 使用空中时间更新学员已完成课时
     */
    boolean createFlightReport(FlightReport flightReport);
    
    /**
     * 更新飞行报告
     * 
     * @param flightReport 飞行报告信息
     *                    - id: 报告ID（必填）
     *                    - 其他需更新的字段
     * @return 操作结果
     */
    boolean updateFlightReport(FlightReport flightReport);
    
    /**
     * 获取飞行报告详情
     * 
     * @param id 报告ID
     * @return 飞行报告详情
     */
    FlightReport getFlightReport(Long id);
    
    /**
     * 分页查询飞行报告（按日期过滤）
     * 
     * @param current 当前页码
     * @param size 每页记录数
     * @param status 报告状态（可选）
     * @param userCourseId 学员课时记录ID（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 飞行报告列表及分页信息
     */
    Page<FlightReport> pageFlightReports(int current, int size, String status, Long userCourseId, 
                                        LocalDate startDate, LocalDate endDate);
	
    /**
     * 获取用户所有飞行报告（按日期过滤）
     * 
     * @param userCourseId 学员课时记录ID
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 飞行报告列表
     */
    List<FlightReport> getUserFlightReports(Long userCourseId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 删除飞行报告（逻辑删除）
     * 报告将被标记为已删除，但记录仍保留在数据库中
     * 
     * @param id 报告ID
     * @return 操作结果
     */
    boolean deleteReport(Long id);
    
    /**
     * 分页查询飞行报告（按学员姓名，同时查找主驾驶和副驾驶）
     *
     * @param current 当前页码
     * @param size 每页记录数
     * @param studentName 学员姓名（查询主驾或副驾包含该姓名的报告）
     * @param status 报告状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 飞行报告列表及分页信息
     */
    Page<FlightReport> pageFlightReportsByStudentName(int current, int size, String studentName,
                                                     String status, LocalDate startDate, LocalDate endDate);
    
    /**
     * 按飞机和日期查询飞行报告
     * 
     * @param aircraftRegNumber 机号
     * @param date 飞行日期
     * @return 飞行报告列表
     */
    List<FlightReport> getFlightReportsByAircraftAndDate(String aircraftRegNumber, LocalDate date);
} 