package org.example.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 时间段DTO
 */
@Data
public class TimeSlotDTO {
    /**
     * 时间段ID
     */
    private Long id;
    
    /**
     * 日期
     */
    private LocalDate date;
    
    /**
     * 开始时间
     */
    private LocalTime startTime;
    
    /**
     * 结束时间
     */
    private LocalTime endTime;
    
    /**
     * 状态:0不可用,1可用
     */
    private Integer status;
    
    /**
     * 用途:ALL,EXPERIENCE_ONLY,TRAINING_ONLY
     */
    private String purpose;
    
    /**
     * 体验已预约
     */
    private Boolean experienceBooked;
    
    /**
     * 训练已预约
     */
    private Boolean trainingBooked;
    
    /**
     * 体验预约容量
     */
    private Integer experienceCapacity;
    
    /**
     * 训练预约容量
     */
    private Integer trainingCapacity;
    
    /**
     * 已预约的体验数量
     */
    private Integer experienceBookedCount;
    
    /**
     * 已预约的训练数量
     */
    private Integer trainingBookedCount;
    
    /**
     * 时间段描述
     */
    private String description;
    
    /**
     * 当前用户可用的容量（根据用户角色动态计算）
     */
    private Integer availableCapacity;
    
    /**
     * 对当前用户是否可预约（根据用户角色动态计算）
     */
    private Boolean isAvailable;
} 