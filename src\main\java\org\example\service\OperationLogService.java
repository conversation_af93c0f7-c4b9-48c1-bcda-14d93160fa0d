package org.example.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.example.entity.OperationLog;

/**
 * 操作日志服务接口
 */
public interface OperationLogService {
    
    /**
     * 记录操作日志
     */
    boolean recordLog(OperationLog operationLog);
    
    /**
     * 根据ID获取操作日志
     */
    OperationLog getLog(Long id);
    
    /**
     * 分页查询操作日志
     */
    Page<OperationLog> pageLog(int current, int size, String module, String operation, Long userId, String startTime, String endTime);
    
    /**
     * 清理过期日志（30天前的日志）
     */
    int clearExpiredLogs(int days);
} 