package org.example.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.example.dto.AircraftDTO;
import org.example.entity.Aircraft;
import org.example.entity.Appointment;
import org.example.mapper.AircraftMapper;
import org.example.mapper.AppointmentMapper;
import org.example.service.AircraftService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞机服务实现类
 */
@Service
@Slf4j
public class AircraftServiceImpl extends ServiceImpl<AircraftMapper, Aircraft> implements AircraftService {

    @Autowired
    private AppointmentMapper appointmentMapper;

    /**
     * 分页获取飞机列表
     *
     * @param page  分页参数
     * @param model 机型，可选
     * @param type  类型，可选
     * @param status 状态，可选
     * @return 分页结果
     */
    @Override
    public Page<AircraftDTO> page(Page<Aircraft> page, String model, String type, String status) {
        LambdaQueryWrapper<Aircraft> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.isNotBlank(model)) {
            queryWrapper.like(Aircraft::getModel, model);
        }
        
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(Aircraft::getType, type);
        }
        
        if (StringUtils.isNotBlank(status)) {
            queryWrapper.eq(Aircraft::getStatus, status);
        }
        
        // 默认按注册号排序
        queryWrapper.orderByAsc(Aircraft::getRegistrationNumber);
        
        // 执行查询
        Page<Aircraft> resultPage = this.page(page, queryWrapper);
        
        // 转换为DTO
        Page<AircraftDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(resultPage, dtoPage, "records");
        
        List<AircraftDTO> dtoList = resultPage.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }

    /**
     * 根据ID获取飞机详情
     *
     * @param id 飞机ID
     * @return 飞机详情
     */
    @Override
    public AircraftDTO getDetail(Long id) {
        Aircraft aircraft = this.getById(id);
        return aircraft != null ? convertToDTO(aircraft) : null;
    }
	
    
    /**
     * 将实体转换为DTO
     */
    private AircraftDTO convertToDTO(Aircraft aircraft) {
        if (aircraft == null) {
            return null;
        }
        
        AircraftDTO dto = new AircraftDTO();
        BeanUtils.copyProperties(aircraft, dto);
        return dto;
    }
} 