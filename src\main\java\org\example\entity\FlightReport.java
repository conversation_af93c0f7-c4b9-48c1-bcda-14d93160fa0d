package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_report")
public class FlightReport extends BaseEntity {

    @TableField("user_course_id")
    private Long userCourseId;

    @TableField("pilot_name")
    private String pilotName;

    @TableField("copilot_name")
    private String copilotName;
    
    @TableField("aircraft_reg_number")
    private String aircraftRegNumber; // 机号，如B-12HC
    
    @TableField("flight_nature")
    private String flightNature; // 任务性质（包含单飞/带飞信息）

    @TableField("departure_airport")
    private String departureAirport;

    @TableField("landing_airport")
    private String landingAirport;

    @TableField("training_type")
    private String trainingType;

    @TableField("flight_date")
    private LocalDate flightDate;
    
    @TableField("startup_time")
    private LocalTime startupTime; // 开车时间（时:分）

    @TableField("takeoff_time")
    private LocalTime takeoffTime; // 起飞时间（时:分）
    
    @TableField("landing_time")
    private LocalTime landingTime; // 着陆时间（时:分）

    @TableField("shutdown_time")
    private LocalTime shutdownTime; // 关车时间（时:分）

    @TableField("landing_count")
    private Integer landingCount;
    
    @TableField("ground_time")
    private BigDecimal groundTime; // 地面时间（小时，小数点后2位）
    
    @TableField("air_time")
    private BigDecimal airTime; // 空中时间（小时，小数点后2位）

    @TableField("total_hours")
    private BigDecimal totalHours; // 合计时间（小时，小数点后2位）

    @TableField("report_status")
    private String reportStatus;

    @TableField("remarks")
    private String remarks;

    @TableField("created_by")
    private Long createdBy;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
    
    @TableField("deleted")
    @TableLogic(value = "0", delval = "1")  // 0=未删除, 1=已删除
    private Integer deleted;
} 