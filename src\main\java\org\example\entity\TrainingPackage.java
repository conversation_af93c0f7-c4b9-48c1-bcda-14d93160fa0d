package org.example.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("training_package")
public class TrainingPackage extends BaseEntity {

    @TableField("package_name")
    private String packageName;

    @TableField("description")
    private String description;

    @TableField("hours")
    private Integer hours;

    @TableField("price")
    private BigDecimal price;

    @TableField("validity_days")
    private Integer validityDays;

    @TableField("status")
    private Integer status;
} 