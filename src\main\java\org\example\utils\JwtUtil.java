package org.example.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.example.config.JwtProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 用于生成和解析JWT令牌
 */
@Component
public class JwtUtil implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);
    
    @Autowired
    private JwtProperties jwtProperties;
    
    private static JwtProperties staticJwtProperties;
    private static Key SECRET_KEY;
    
    @Override
    public void afterPropertiesSet() {
        staticJwtProperties = jwtProperties;
        // 初始化静态密钥
        SECRET_KEY = Keys.hmacShaKeyFor(staticJwtProperties.getSecret().getBytes(StandardCharsets.UTF_8));
        logger.info("JWT工具类初始化完成，从配置文件加载密钥");
    }
    
    /**
     * 创建Token
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param role 角色
     * @return JWT Token
     */
    public static String createToken(Long userId, String username, String role) {
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("role", role);
        
        Date now = new Date();
        // 从配置文件获取过期时间（小时）
        long expireTimeMillis = staticJwtProperties.getExpireTime() * 60 * 60 * 1000;
        Date expiration = new Date(now.getTime() + expireTimeMillis);
        logger.debug("Token过期时间设置为: {}", expiration);
        
        String token = Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(SECRET_KEY)
                .compact();
        
        // 验证生成的token是否有效
        try {
            Claims parsedClaims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            logger.info("Token验证成功，可以正确解析");
        } catch (Exception e) {
            logger.error("生成的Token无法被验证: {}", e.getMessage());
        }
        
        return token;
    }

    /**
     * 解析Token
     *
     * @param token JWT Token
     * @return Claims 声明
     */
    public static Claims parseToken(String token) {
        if (!StringUtils.hasText(token)) {
            logger.warn("尝试解析空token");
            return null;
        }
        
        // 只去除首尾空格，不处理中间的字符
        String originalToken = token;
        token = token.trim();
        
        if (!token.equals(originalToken)) {
            logger.info("Token被trim处理，原长度: {}，处理后长度: {}", originalToken.length(), token.length());
        }
        
        Claims claims = null;
        Exception lastException = null;
        
        // 尝试方法1：直接解析
        try {
            logger.debug("尝试方法1 - 直接解析token: [{}]，长度: {}", token, token.length());
            claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            logger.debug("方法1成功，Token解析成功，claims: {}", claims);
            return claims;
        } catch (Exception e) {
            logger.error("方法1失败，Token解析失败: {}，token长度: {}", e.getMessage(), token.length());
            lastException = e;
        }
        
        // 尝试方法2：如果有Bearer前缀，尝试移除
        if (token.startsWith("Bearer ")) {
            try {
                String tokenWithoutPrefix = token.substring(7).trim();
                logger.debug("尝试方法2 - 移除Bearer前缀后解析: [{}]，长度: {}", tokenWithoutPrefix, tokenWithoutPrefix.length());
                claims = Jwts.parserBuilder()
                        .setSigningKey(SECRET_KEY)
                        .build()
                        .parseClaimsJws(tokenWithoutPrefix)
                        .getBody();
                
                logger.debug("方法2成功，Token解析成功，claims: {}", claims);
                return claims;
            } catch (Exception e) {
                logger.error("方法2失败，Token解析失败: {}", e.getMessage());
                lastException = e;
            }
        }
        
        // 尝试方法3：移除所有空格
        try {
            String tokenNoSpaces = token.replaceAll("\\s+", "");
            logger.debug("尝试方法3 - 移除所有空格后解析: [{}]，长度: {}", tokenNoSpaces, tokenNoSpaces.length());
            claims = Jwts.parserBuilder()
                    .setSigningKey(SECRET_KEY)
                    .build()
                    .parseClaimsJws(tokenNoSpaces)
                    .getBody();
            
            logger.debug("方法3成功，Token解析成功，claims: {}", claims);
            return claims;
        } catch (Exception e) {
            logger.error("方法3失败，Token解析失败: {}", e.getMessage());
            lastException = e;
        }
        
        // 所有方法都失败
        if (lastException != null) {
            logger.error("所有解析方法都失败，最后的异常: {}", lastException.getMessage(), lastException);
        }
        return null;
    }

    /**
     * 校验Token是否有效
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public static boolean validateToken(String token) {
        logger.debug("开始验证token: {}", token);
        Claims claims = parseToken(token);
        
        if (claims == null) {
            logger.warn("Token无效，无法解析");
            return false;
        }
        
        Date expiration = claims.getExpiration();
        boolean isValid = !expiration.before(new Date());
        
        if (isValid) {
            logger.debug("Token有效，过期时间: {}", expiration);
        } else {
            logger.warn("Token已过期，过期时间: {}", expiration);
        }
        
        return isValid;
    }

    /**
     * 从Token获取用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public static Long getUserId(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return ((Number) claims.get("userId")).longValue();
        }
        return null;
    }

    /**
     * 从Token获取用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public static String getUsername(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return (String) claims.get("username");
        }
        return null;
    }

    /**
     * 从Token获取用户角色
     *
     * @param token JWT Token
     * @return 用户角色
     */
    public static String getRole(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return (String) claims.get("role");
        }
        return null;
    }
    
    /**
     * 从Authorization头部获取用户ID
     * 用于处理前端传来的"Bearer token"格式的Authorization头
     *
     * @param authHeader Authorization头部值
     * @return 用户ID
     */
    public static Long getUserIdFromToken(String authHeader) {
        if (authHeader == null || authHeader.isEmpty()) {
            return null;
        }
        
        // 如果有Bearer前缀，移除它
        String token = authHeader;
        if (authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }
        
        // 使用已有方法获取用户ID
        return getUserId(token);
    }
} 